"""
Comprehensive test suite for enhanced stock scanner features.

This module tests the new yfinance integration, social media news aggregation,
weighted sentiment analysis, and trading recommendations.
"""

import unittest
import os
import tempfile
from unittest.mock import patch, Mock, MagicMock
import pandas as pd
import numpy as np

# Import modules to test
from stock_data import (
    get_stock_info, 
    get_historical_data, 
    calculate_technical_indicators,
    get_multiple_stock_data,
    get_stock_status,
    StockDataError,
    TickerNotFoundError
)
from social_media_news import (
    get_reddit_posts,
    get_newsapi_articles,
    get_alpha_vantage_news,
    get_free_news_sources,
    extract_headlines_from_sources,
    get_social_media_status
)
from stock_scanner_app import (
    calculate_weighted_sentiment,
    get_sentiment_confidence,
    generate_trading_recommendation
)

class TestStockData(unittest.TestCase):
    """Test cases for stock data functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_ticker = "AAPL"
        self.invalid_ticker = "INVALID123"
    
    @patch('stock_data.yf.Ticker')
    def test_get_stock_info_success(self, mock_ticker):
        """Test successful stock info retrieval."""
        # Mock yfinance response
        mock_stock = Mock()
        mock_stock.info = {
            'symbol': 'AAPL',
            'longName': 'Apple Inc.',
            'sector': 'Technology',
            'currentPrice': 150.0,
            'previousClose': 148.0,
            'marketCap': 2500000000000,
            'trailingPE': 25.0
        }
        mock_ticker.return_value = mock_stock
        
        result = get_stock_info(self.test_ticker)
        
        self.assertEqual(result['symbol'], 'AAPL')
        self.assertEqual(result['company_name'], 'Apple Inc.')
        self.assertEqual(result['current_price'], 150.0)
        self.assertEqual(result['price_change'], 2.0)
        self.assertAlmostEqual(result['price_change_percent'], 1.35, places=2)
    
    @patch('stock_data.yf.Ticker')
    def test_get_stock_info_not_found(self, mock_ticker):
        """Test handling of invalid ticker."""
        mock_stock = Mock()
        mock_stock.info = {}
        mock_ticker.return_value = mock_stock
        
        with self.assertRaises(TickerNotFoundError):
            get_stock_info(self.invalid_ticker)
    
    def test_validate_ticker_symbol(self):
        """Test ticker symbol validation."""
        from stock_data import validate_ticker_symbol
        
        # Valid tickers
        self.assertEqual(validate_ticker_symbol("AAPL"), "AAPL")
        self.assertEqual(validate_ticker_symbol("  tsla  "), "TSLA")
        self.assertEqual(validate_ticker_symbol("BRK.A"), "BRK.A")
        
        # Invalid tickers
        with self.assertRaises(StockDataError):
            validate_ticker_symbol("")
        with self.assertRaises(StockDataError):
            validate_ticker_symbol("INVALID@TICKER")
        with self.assertRaises(StockDataError):
            validate_ticker_symbol(None)
    
    @patch('stock_data.yf.Ticker')
    def test_get_historical_data(self, mock_ticker):
        """Test historical data retrieval."""
        # Create mock historical data
        dates = pd.date_range('2023-01-01', periods=10, freq='D')
        mock_hist_data = pd.DataFrame({
            'Open': np.random.uniform(140, 160, 10),
            'High': np.random.uniform(145, 165, 10),
            'Low': np.random.uniform(135, 155, 10),
            'Close': np.random.uniform(140, 160, 10),
            'Volume': np.random.randint(1000000, 10000000, 10)
        }, index=dates)
        
        mock_stock = Mock()
        mock_stock.history.return_value = mock_hist_data
        mock_ticker.return_value = mock_stock
        
        result = get_historical_data(self.test_ticker)
        
        self.assertFalse(result.empty)
        self.assertIn('close', result.columns)
        self.assertIn('daily_return', result.columns)
        self.assertIn('cumulative_return', result.columns)
    
    def test_calculate_technical_indicators(self):
        """Test technical indicators calculation."""
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=60, freq='D')
        sample_data = pd.DataFrame({
            'close': np.random.uniform(140, 160, 60),
            'high': np.random.uniform(145, 165, 60),
            'low': np.random.uniform(135, 155, 60),
            'volume': np.random.randint(1000000, 10000000, 60)
        }, index=dates)
        
        result = calculate_technical_indicators(sample_data)
        
        # Check that technical indicators are added
        self.assertIn('sma_20', result.columns)
        self.assertIn('sma_50', result.columns)
        self.assertIn('rsi', result.columns)
        self.assertIn('macd', result.columns)
        self.assertIn('bb_upper', result.columns)
        self.assertIn('bb_lower', result.columns)

class TestSocialMediaNews(unittest.TestCase):
    """Test cases for social media news functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_ticker = "AAPL"
    
    @patch('social_media_news.requests.get')
    def test_get_reddit_posts_success(self, mock_get):
        """Test successful Reddit posts retrieval."""
        # Mock Reddit API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'data': {
                'children': [
                    {
                        'data': {
                            'title': 'AAPL stock discussion',
                            'selftext': 'Apple stock analysis',
                            'score': 100,
                            'num_comments': 50,
                            'created_utc': 1640995200,
                            'permalink': '/r/stocks/comments/test',
                            'author': 'testuser'
                        }
                    }
                ]
            }
        }
        mock_get.return_value = mock_response
        
        result = get_reddit_posts(self.test_ticker, limit=5)
        
        self.assertIsInstance(result, list)
        if result:  # If posts were found
            self.assertIn('source', result[0])
            self.assertEqual(result[0]['source'], 'reddit')
    
    @patch.dict(os.environ, {'NEWSAPI_KEY': 'test_key'})
    @patch('social_media_news.requests.get')
    def test_get_newsapi_articles_success(self, mock_get):
        """Test successful NewsAPI articles retrieval."""
        # Mock NewsAPI response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'status': 'ok',
            'articles': [
                {
                    'title': 'Apple Stock News',
                    'description': 'Apple stock analysis',
                    'url': 'https://example.com/news',
                    'publishedAt': '2023-01-01T12:00:00Z',
                    'source': {'name': 'Test News'},
                    'author': 'Test Author'
                }
            ]
        }
        mock_get.return_value = mock_response
        
        result = get_newsapi_articles(self.test_ticker, limit=5)
        
        self.assertIsInstance(result, list)
        if result:
            self.assertIn('source', result[0])
            self.assertEqual(result[0]['source'], 'newsapi')
    
    def test_extract_headlines_from_sources(self):
        """Test headline extraction from various sources."""
        sources_data = {
            'reddit': [
                {
                    'title': 'Reddit post about AAPL',
                    'text': 'Some discussion text'
                }
            ],
            'newsapi': [
                {
                    'title': 'News article about Apple',
                    'description': 'Article description'
                }
            ]
        }
        
        headlines = extract_headlines_from_sources(sources_data)
        
        self.assertIsInstance(headlines, list)
        self.assertIn('Reddit post about AAPL', headlines)
        self.assertIn('News article about Apple', headlines)

class TestWeightedSentiment(unittest.TestCase):
    """Test cases for weighted sentiment analysis."""
    
    def test_calculate_weighted_sentiment(self):
        """Test weighted sentiment calculation."""
        traditional_headlines = [
            "Apple stock rises on strong earnings",
            "Positive outlook for Apple"
        ]
        social_headlines = [
            "AAPL to the moon!",
            "Bullish on Apple stock"
        ]
        
        with patch('stock_scanner_app.analyze_sentiment_safe') as mock_sentiment:
            # Mock sentiment scores
            mock_sentiment.side_effect = [0.5, 0.3, 0.7, 0.4]  # Traditional: 0.5, 0.3; Social: 0.7, 0.4
            
            result = calculate_weighted_sentiment(traditional_headlines, social_headlines)
            
            self.assertIn('traditional_sentiment', result)
            self.assertIn('social_sentiment', result)
            self.assertIn('weighted_sentiment', result)
            self.assertEqual(result['traditional_count'], 2)
            self.assertEqual(result['social_count'], 2)
    
    def test_get_sentiment_confidence(self):
        """Test sentiment confidence calculation."""
        # High confidence scenario (consistent scores)
        high_conf_scores = [0.5, 0.6, 0.55, 0.52, 0.58, 0.54, 0.56, 0.53, 0.57, 0.51]
        result = get_sentiment_confidence(high_conf_scores)
        
        self.assertEqual(result['confidence'], 'High')
        self.assertGreater(result['consistency'], 0.7)
        
        # Low confidence scenario (inconsistent scores)
        low_conf_scores = [0.8, -0.5, 0.2]
        result = get_sentiment_confidence(low_conf_scores)
        
        self.assertEqual(result['confidence'], 'Low')
        self.assertLess(result['consistency'], 0.7)

class TestTradingRecommendations(unittest.TestCase):
    """Test cases for trading recommendations."""
    
    def test_generate_trading_recommendation_buy(self):
        """Test buy recommendation generation."""
        stock_data = {
            'price_change_percent': 3.5,
            'pe_ratio': 12.0,
            'market_cap': 2500000000000
        }
        sentiment_data = {
            'weighted_sentiment': 0.4
        }
        confidence_metrics = {
            'confidence': 'High'
        }
        
        result = generate_trading_recommendation(stock_data, sentiment_data, confidence_metrics)
        
        self.assertIn('action', result)
        self.assertIn('confidence', result)
        self.assertIn('reasoning', result)
        self.assertIn('score', result)
        self.assertIn(result['action'], ['BUY', 'WEAK BUY', 'HOLD', 'WEAK SELL', 'SELL'])
    
    def test_generate_trading_recommendation_sell(self):
        """Test sell recommendation generation."""
        stock_data = {
            'price_change_percent': -6.0,
            'pe_ratio': 35.0
        }
        sentiment_data = {
            'weighted_sentiment': -0.5
        }
        confidence_metrics = {
            'confidence': 'High'
        }
        
        result = generate_trading_recommendation(stock_data, sentiment_data, confidence_metrics)
        
        self.assertIn(result['action'], ['SELL', 'WEAK SELL'])
        self.assertLess(result['score'], 0)

class TestIntegration(unittest.TestCase):
    """Integration tests for the enhanced features."""
    
    def test_configuration_validation(self):
        """Test that configuration validation includes new modules."""
        from config_validator import run_full_configuration_check
        
        result = run_full_configuration_check()
        
        self.assertIn('stock_data', result)
        self.assertIn('social_media_news', result)
        self.assertIn('overall_valid', result)

if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestStockData))
    test_suite.addTest(unittest.makeSuite(TestSocialMediaNews))
    test_suite.addTest(unittest.makeSuite(TestWeightedSentiment))
    test_suite.addTest(unittest.makeSuite(TestTradingRecommendations))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"{'='*50}")
