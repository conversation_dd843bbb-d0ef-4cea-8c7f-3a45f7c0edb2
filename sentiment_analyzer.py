import nltk
import logging
from typing import Optional, Dict, Any
from nltk.sentiment.vader import SentimentIntensityAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SentimentAnalysisError(Exception):
    """Custom exception for sentiment analysis related errors."""
    pass

class NLTKResourceError(SentimentAnalysisError):
    """Exception raised when NLTK resources are not available."""
    pass

def ensure_vader_lexicon() -> bool:
    """
    Ensure VADER lexicon is downloaded and available.

    Returns:
        bool: True if lexicon is available, False otherwise

    Raises:
        NLTKResourceError: If unable to download or access the lexicon
    """
    try:
        # Check if VADER lexicon is already available
        nltk.data.find('vader_lexicon')
        logger.info("VADER lexicon found")
        return True
    except LookupError:
        logger.info("VADER lexicon not found, attempting to download...")
        try:
            # Attempt to download VADER lexicon
            result = nltk.download('vader_lexicon', quiet=True)

            # Check if download was successful
            if result:
                # Verify download was successful
                nltk.data.find('vader_lexicon')
                logger.info("VADER lexicon downloaded successfully")
                return True
            else:
                # Try alternative download method
                logger.info("Trying alternative download method...")
                nltk.download('vader_lexicon', quiet=False)
                nltk.data.find('vader_lexicon')
                logger.info("VADER lexicon downloaded successfully (alternative method)")
                return True
        except Exception as e:
            logger.error(f"Failed to download VADER lexicon: {e}")
            # Try to use the analyzer anyway - it might work
            try:
                from nltk.sentiment.vader import SentimentIntensityAnalyzer
                analyzer = SentimentIntensityAnalyzer()
                test_result = analyzer.polarity_scores("test")
                if test_result:
                    logger.info("VADER analyzer working despite download error")
                    return True
            except Exception as e2:
                logger.error(f"VADER analyzer also failed: {e2}")

            raise NLTKResourceError(
                f"Unable to download VADER lexicon. Please check your internet connection "
                f"and try again. Error: {e}"
            )

def validate_text_input(text: Any) -> str:
    """
    Validate and sanitize text input for sentiment analysis.

    Args:
        text: Input text to validate

    Returns:
        str: Cleaned and validated text

    Raises:
        SentimentAnalysisError: If text is invalid or empty
    """
    if text is None:
        raise SentimentAnalysisError("Text input cannot be None")

    if not isinstance(text, str):
        # Try to convert to string
        try:
            text = str(text)
        except Exception as e:
            raise SentimentAnalysisError(f"Unable to convert input to string: {e}")

    # Clean and validate text
    text = text.strip()

    if not text:
        raise SentimentAnalysisError("Text input cannot be empty or whitespace only")

    # Check for reasonable text length (avoid extremely long texts)
    if len(text) > 10000:  # 10k characters limit
        logger.warning(f"Text is very long ({len(text)} characters), truncating to 10000 characters")
        text = text[:10000]

    return text

def analyze_sentiment(text: Any) -> Optional[float]:
    """
    Analyzes sentiment of a text using VADER and returns the compound score.

    Args:
        text: The text to analyze (will be converted to string if possible)

    Returns:
        Optional[float]: Compound sentiment score between -1 and 1, or None if analysis fails

    Raises:
        SentimentAnalysisError: For various sentiment analysis errors
    """
    try:
        # Ensure VADER lexicon is available
        ensure_vader_lexicon()

        # Validate and clean input text
        clean_text = validate_text_input(text)

        # Initialize analyzer
        try:
            analyzer = SentimentIntensityAnalyzer()
        except Exception as e:
            logger.error(f"Failed to initialize SentimentIntensityAnalyzer: {e}")
            raise SentimentAnalysisError(f"Failed to initialize sentiment analyzer: {e}")

        # Perform sentiment analysis
        try:
            scores = analyzer.polarity_scores(clean_text)
            compound_score = scores.get('compound')

            if compound_score is None:
                raise SentimentAnalysisError("Sentiment analysis returned None for compound score")

            # Validate score is in expected range
            if not isinstance(compound_score, (int, float)) or not (-1 <= compound_score <= 1):
                logger.warning(f"Unexpected compound score: {compound_score}")
                return 0.0  # Return neutral score as fallback

            logger.debug(f"Sentiment analysis successful: {compound_score}")
            return float(compound_score)

        except Exception as e:
            logger.error(f"Error during sentiment analysis: {e}")
            raise SentimentAnalysisError(f"Failed to analyze sentiment: {e}")

    except NLTKResourceError:
        # Re-raise NLTK resource errors
        raise
    except SentimentAnalysisError:
        # Re-raise sentiment analysis errors
        raise
    except Exception as e:
        logger.error(f"Unexpected error in sentiment analysis: {e}")
        raise SentimentAnalysisError(f"Unexpected error occurred: {e}")

def analyze_sentiment_safe(text: Any, default_score: float = 0.0) -> float:
    """
    Safe version of analyze_sentiment that returns a default score on error.

    Args:
        text: The text to analyze
        default_score: Score to return if analysis fails (default: 0.0 for neutral)

    Returns:
        float: Sentiment score or default_score if analysis fails
    """
    try:
        result = analyze_sentiment(text)
        return result if result is not None else default_score
    except Exception as e:
        logger.warning(f"Sentiment analysis failed, returning default score {default_score}: {e}")
        return default_score

def get_sentiment_analyzer_status() -> Dict[str, Any]:
    """
    Check the status of the sentiment analyzer and its dependencies.

    Returns:
        Dict[str, Any]: Status information about the sentiment analyzer
    """
    status = {
        "nltk_available": False,
        "vader_lexicon_available": False,
        "analyzer_functional": False,
        "error_message": None
    }

    try:
        # Check if NLTK is available
        import nltk
        status["nltk_available"] = True

        # Check if VADER lexicon is available
        try:
            ensure_vader_lexicon()
            status["vader_lexicon_available"] = True
        except Exception as e:
            status["error_message"] = f"VADER lexicon error: {e}"
            return status

        # Test analyzer functionality
        try:
            test_result = analyze_sentiment("This is a test sentence.")
            if test_result is not None:
                status["analyzer_functional"] = True
            else:
                status["error_message"] = "Analyzer returned None for test input"
        except Exception as e:
            status["error_message"] = f"Analyzer test failed: {e}"

    except ImportError as e:
        status["error_message"] = f"NLTK import error: {e}"
    except Exception as e:
        status["error_message"] = f"Unexpected error: {e}"

    return status