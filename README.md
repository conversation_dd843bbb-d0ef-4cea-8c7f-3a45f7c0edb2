# Enhanced Stock Scanner with yfinance and Social Media Integration

A comprehensive stock analysis tool that combines traditional financial data with sentiment analysis from news and social media sources to provide trading recommendations.

## 🚀 Features

### Core Features
- **Real-time Stock Data**: Live stock prices, financial metrics, and company information via yfinance
- **News Sentiment Analysis**: Traditional news sentiment using NLTK's VADER
- **Social Media Integration**: Reddit posts and discussions analysis
- **Interactive Charts**: Candlestick charts with technical indicators
- **Trading Recommendations**: AI-powered buy/sell/hold recommendations

### Enhanced Analytics
- **Weighted Sentiment Scoring**: Combines traditional news (70%) and social media (30%) sentiment
- **Technical Indicators**: SMA, EMA, MACD, RSI, Bollinger Bands
- **Confidence Metrics**: Sentiment analysis confidence and consistency scores
- **Portfolio Dashboard**: Overview of multiple stocks with aggregated metrics

### Data Sources
- **Stock Data**: Yahoo Finance (via yfinance) - Free, no API key required
- **Traditional News**: Marketaux API (requires free API key)
- **Social Media**: Reddit (free, no API key required)
- **Optional Sources**: NewsAPI, Alpha Vantage (require free API keys)

## 📦 Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd streamlit_stock_scanner
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   Create a `.env` file in the project root:
   ```env
   # Required
   MARKETAUX_API_KEY=your_marketaux_api_key_here
   
   # Optional (for enhanced features)
   NEWSAPI_KEY=your_newsapi_key_here
   ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
   ```

4. **Get API Keys** (Free):
   - **Marketaux** (Required): https://www.marketaux.com/
   - **NewsAPI** (Optional): https://newsapi.org/
   - **Alpha Vantage** (Optional): https://www.alphavantage.co/

## 🎯 Quick Start

1. **Run the application**:
   ```bash
   streamlit run stock_scanner_app.py
   ```

2. **Enter stock tickers** (e.g., AAPL, TSLA, GOOGL)

3. **Configure analysis options** in the sidebar:
   - Enable/disable stock data and metrics
   - Enable/disable social media news
   - Enable/disable interactive charts
   - Set chart period and historical analysis days

4. **Click "Scan Stocks"** to analyze

## 📊 Understanding the Results

### Stock Metrics
- **Current Price**: Real-time stock price with change indicators
- **Market Cap**: Company market capitalization
- **P/E Ratio**: Price-to-earnings ratio for valuation
- **Volume**: Trading volume and beta coefficient
- **52-Week Range**: Annual high and low prices

### Sentiment Analysis
- **Weighted Sentiment**: Combined score from news and social media
- **Traditional News**: Sentiment from financial news sources
- **Social Media**: Sentiment from Reddit discussions
- **Confidence Level**: Reliability of sentiment analysis

### Trading Recommendations
- **BUY/SELL/HOLD**: Action recommendation with confidence level
- **Risk Level**: Associated risk assessment
- **Analysis**: Detailed reasoning for the recommendation
- **Score**: Numerical score (-1.0 to +1.0) indicating strength

## 🔧 Configuration

### Sidebar Options
- **Historical Tracking**: Enable sentiment tracking over time
- **Stock Data & Metrics**: Real-time financial data
- **Social Media News**: Reddit and other social sources
- **Interactive Charts**: Technical analysis charts
- **Chart Period**: Time range for price charts

### System Status
The sidebar shows the status of all components:
- ✅ System Ready: All components working
- ⚠️ Warnings: Non-critical issues
- ❌ Configuration Issues: Critical problems requiring attention

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
python test_enhanced_features.py

# Run specific test categories
python -m pytest test_enhanced_features.py::TestStockData -v
python -m pytest test_enhanced_features.py::TestSocialMediaNews -v
python -m pytest test_enhanced_features.py::TestWeightedSentiment -v
```

## 📈 Technical Indicators

The application calculates and displays:

- **Simple Moving Averages (SMA)**: 20-day and 50-day
- **Exponential Moving Averages (EMA)**: 12-day and 26-day
- **MACD**: Moving Average Convergence Divergence
- **RSI**: Relative Strength Index (14-day)
- **Bollinger Bands**: 20-day with 2 standard deviations

## 🤖 Trading Algorithm

The recommendation engine considers:

1. **Sentiment Analysis (40% weight)**:
   - Weighted combination of news and social media sentiment
   - Confidence level adjustments

2. **Price Momentum (30% weight)**:
   - Recent price changes and trends
   - Volume analysis

3. **Valuation Metrics (20% weight)**:
   - P/E ratio analysis
   - Market cap considerations

4. **Confidence Adjustment (10% weight)**:
   - Sentiment analysis reliability
   - Data quality metrics

## ⚠️ Disclaimers

- **Educational Purpose**: This tool is for educational and research purposes only
- **Not Financial Advice**: Do not use as sole basis for investment decisions
- **Risk Warning**: All investments carry risk of loss
- **Data Accuracy**: Real-time data may have delays or inaccuracies
- **API Limitations**: Free APIs have rate limits and usage restrictions

## 🛠️ Troubleshooting

### Common Issues

1. **API Key Errors**:
   - Verify API keys in `.env` file
   - Check API key validity and permissions
   - Ensure no extra spaces or characters

2. **Rate Limit Errors**:
   - Wait before retrying
   - Consider upgrading to paid API plans
   - Reduce number of stocks analyzed simultaneously

3. **Import Errors**:
   - Install missing dependencies: `pip install -r requirements.txt`
   - Check Python version compatibility (3.8+)

4. **Chart Display Issues**:
   - Ensure plotly is installed
   - Check browser compatibility
   - Try refreshing the page

### Performance Tips

- **Limit Stock Count**: Analyze 5-10 stocks at a time for best performance
- **Use Caching**: Enable historical tracking for better caching
- **API Management**: Monitor API usage to avoid rate limits
- **Browser Resources**: Close other tabs for better chart performance

## 📝 License

This project is for educational purposes. Please respect API terms of service and rate limits.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review API documentation
3. Check system status in the sidebar
4. Verify configuration settings
