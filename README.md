# Streamlined Stock Scanner

A compact stock analysis tool that combines real-time financial data with sentiment analysis from news and social media sources.

## 🚀 Features

- **Real-time Stock Data**: Live prices, financial metrics via yfinance (free)
- **News Sentiment**: Traditional news + Reddit sentiment analysis
- **Interactive Charts**: Candlestick charts with technical indicators
- **Trading Recommendations**: Simple buy/sell/hold recommendations
- **Multiple Sources**: Marketaux news + Reddit + optional NewsAPI

## 📦 Quick Setup

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Create `.env` file**:
   ```env
   MARKETAUX_API_KEY=your_key_here
   NEWSAPI_KEY=your_key_here  # Optional
   ```

3. **Get free API keys**:
   - **Marketaux**: https://www.marketaux.com/ (required)
   - **NewsAPI**: https://newsapi.org/ (optional)

4. **Run the app**:
   ```bash
   streamlit run app.py
   ```

## 🎯 Usage

1. Enter stock tickers (e.g., `AAPL, TSLA, GOOGL`)
2. Click "Analyze Stocks"
3. View results with:
   - Stock metrics (price, P/E, market cap)
   - Sentiment analysis from news + social media
   - Trading recommendation with reasoning
   - Interactive price charts

## 📊 What You Get

- **Stock Data**: Current price, market cap, P/E ratio, volume
- **Sentiment Score**: Combined news + Reddit sentiment (-1 to +1)
- **Recommendation**: BUY/SELL/HOLD with confidence level
- **Price Charts**: Candlestick with moving averages and volume

## 🧪 Testing

```bash
python test_app.py
```

## ⚠️ Disclaimer

Educational purposes only. Not financial advice. Always do your own research before investing.

## 📁 Project Structure

```
├── app.py              # Main Streamlit application
├── data_sources.py     # All data fetching (stocks, news, sentiment)
├── requirements.txt    # Dependencies
├── test_app.py        # Simple tests
└── .env.example       # Environment variables template
```

Only 4 core files - much simpler than before!
