"""
Social media news aggregator module for fetching news from multiple free sources.

This module integrates with various free APIs to gather news and social media
sentiment data for stocks, including Reddit, NewsAPI, and other sources.
"""

import requests
import os
import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dotenv import load_dotenv
import json

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration constants
MAX_RETRIES = 3
RETRY_DELAY = 1
TIMEOUT = 30
MAX_ARTICLES_PER_SOURCE = 20

class SocialMediaNewsError(Exception):
    """Custom exception for social media news related errors."""
    pass

class APIKeyError(SocialMediaNewsError):
    """Exception raised when API key is missing or invalid."""
    pass

class RateLimitError(SocialMediaNewsError):
    """Exception raised when API rate limit is exceeded."""
    pass

def get_reddit_posts(ticker: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Fetch Reddit posts mentioning a stock ticker from finance-related subreddits.
    Uses Reddit's public JSON API (no authentication required).
    
    Args:
        ticker: Stock ticker symbol
        limit: Maximum number of posts to fetch
        
    Returns:
        List[Dict[str, Any]]: List of Reddit posts
    """
    posts = []
    
    # Finance-related subreddits to search
    subreddits = ['stocks', 'investing', 'SecurityAnalysis', 'ValueInvesting', 'StockMarket']
    
    try:
        for subreddit in subreddits[:2]:  # Limit to avoid rate limits
            try:
                # Reddit JSON API endpoint
                url = f"https://www.reddit.com/r/{subreddit}/search.json"
                params = {
                    'q': ticker,
                    'restrict_sr': 'true',
                    'sort': 'new',
                    'limit': min(limit, 10),
                    't': 'week'  # Posts from the last week
                }
                
                headers = {
                    'User-Agent': 'StockScanner/1.0 (Educational Purpose)'
                }
                
                response = requests.get(url, params=params, headers=headers, timeout=TIMEOUT)
                response.raise_for_status()
                
                data = response.json()
                
                if 'data' in data and 'children' in data['data']:
                    for post in data['data']['children']:
                        post_data = post.get('data', {})
                        
                        # Extract relevant information
                        reddit_post = {
                            'source': 'reddit',
                            'subreddit': subreddit,
                            'title': post_data.get('title', ''),
                            'text': post_data.get('selftext', '')[:500],  # Limit text length
                            'score': post_data.get('score', 0),
                            'num_comments': post_data.get('num_comments', 0),
                            'created_utc': post_data.get('created_utc', 0),
                            'url': f"https://reddit.com{post_data.get('permalink', '')}",
                            'author': post_data.get('author', 'unknown')
                        }
                        
                        # Only include posts that actually mention the ticker
                        if ticker.upper() in reddit_post['title'].upper() or ticker.upper() in reddit_post['text'].upper():
                            posts.append(reddit_post)
                
                # Rate limiting - be respectful to Reddit
                time.sleep(1)
                
            except Exception as e:
                logger.warning(f"Error fetching from r/{subreddit}: {e}")
                continue
        
        logger.info(f"Fetched {len(posts)} Reddit posts for {ticker}")
        return posts[:limit]
        
    except Exception as e:
        logger.error(f"Error fetching Reddit posts for {ticker}: {e}")
        return []

def get_newsapi_articles(ticker: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Fetch news articles from NewsAPI (requires free API key).
    
    Args:
        ticker: Stock ticker symbol
        limit: Maximum number of articles to fetch
        
    Returns:
        List[Dict[str, Any]]: List of news articles
    """
    articles = []
    api_key = os.getenv('NEWSAPI_KEY')
    
    if not api_key:
        logger.warning("NEWSAPI_KEY not found in environment variables")
        return []
    
    try:
        # NewsAPI endpoint for everything
        url = "https://newsapi.org/v2/everything"
        
        # Calculate date range (last 7 days)
        to_date = datetime.now()
        from_date = to_date - timedelta(days=7)
        
        params = {
            'q': f'"{ticker}" OR "{ticker} stock" OR "{ticker} shares"',
            'language': 'en',
            'sortBy': 'publishedAt',
            'from': from_date.strftime('%Y-%m-%d'),
            'to': to_date.strftime('%Y-%m-%d'),
            'pageSize': min(limit, 20),
            'apiKey': api_key
        }
        
        response = requests.get(url, params=params, timeout=TIMEOUT)
        
        if response.status_code == 401:
            raise APIKeyError("Invalid NewsAPI key")
        elif response.status_code == 429:
            raise RateLimitError("NewsAPI rate limit exceeded")
        
        response.raise_for_status()
        data = response.json()
        
        if data.get('status') == 'ok' and 'articles' in data:
            for article in data['articles']:
                news_article = {
                    'source': 'newsapi',
                    'source_name': article.get('source', {}).get('name', 'Unknown'),
                    'title': article.get('title', ''),
                    'description': article.get('description', ''),
                    'content': article.get('content', '')[:500] if article.get('content') else '',
                    'url': article.get('url', ''),
                    'published_at': article.get('publishedAt', ''),
                    'author': article.get('author', 'Unknown')
                }
                articles.append(news_article)
        
        logger.info(f"Fetched {len(articles)} NewsAPI articles for {ticker}")
        return articles
        
    except APIKeyError:
        raise
    except RateLimitError:
        raise
    except Exception as e:
        logger.error(f"Error fetching NewsAPI articles for {ticker}: {e}")
        return []

def get_alpha_vantage_news(ticker: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Fetch news from Alpha Vantage News API (requires free API key).
    
    Args:
        ticker: Stock ticker symbol
        limit: Maximum number of articles to fetch
        
    Returns:
        List[Dict[str, Any]]: List of news articles
    """
    articles = []
    api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
    
    if not api_key:
        logger.warning("ALPHA_VANTAGE_API_KEY not found in environment variables")
        return []
    
    try:
        url = "https://www.alphavantage.co/query"
        params = {
            'function': 'NEWS_SENTIMENT',
            'tickers': ticker,
            'limit': min(limit, 50),
            'apikey': api_key
        }
        
        response = requests.get(url, params=params, timeout=TIMEOUT)
        response.raise_for_status()
        
        data = response.json()
        
        if 'feed' in data:
            for item in data['feed'][:limit]:
                article = {
                    'source': 'alpha_vantage',
                    'title': item.get('title', ''),
                    'summary': item.get('summary', ''),
                    'url': item.get('url', ''),
                    'time_published': item.get('time_published', ''),
                    'authors': item.get('authors', []),
                    'overall_sentiment_score': item.get('overall_sentiment_score', 0),
                    'overall_sentiment_label': item.get('overall_sentiment_label', 'Neutral'),
                    'ticker_sentiment': item.get('ticker_sentiment', [])
                }
                articles.append(article)
        
        logger.info(f"Fetched {len(articles)} Alpha Vantage articles for {ticker}")
        return articles
        
    except Exception as e:
        logger.error(f"Error fetching Alpha Vantage news for {ticker}: {e}")
        return []

def get_free_news_sources(ticker: str, max_per_source: int = 10) -> Dict[str, List[Dict[str, Any]]]:
    """
    Aggregate news from multiple free sources.
    
    Args:
        ticker: Stock ticker symbol
        max_per_source: Maximum articles per source
        
    Returns:
        Dict[str, List[Dict[str, Any]]]: Dictionary mapping source names to articles
    """
    all_sources = {}
    
    # Reddit posts (always available)
    try:
        reddit_posts = get_reddit_posts(ticker, max_per_source)
        if reddit_posts:
            all_sources['reddit'] = reddit_posts
    except Exception as e:
        logger.warning(f"Failed to fetch Reddit posts: {e}")
    
    # NewsAPI (requires API key)
    try:
        newsapi_articles = get_newsapi_articles(ticker, max_per_source)
        if newsapi_articles:
            all_sources['newsapi'] = newsapi_articles
    except APIKeyError as e:
        logger.warning(f"NewsAPI key error: {e}")
    except RateLimitError as e:
        logger.warning(f"NewsAPI rate limit: {e}")
    except Exception as e:
        logger.warning(f"Failed to fetch NewsAPI articles: {e}")
    
    # Alpha Vantage (requires API key)
    try:
        alpha_articles = get_alpha_vantage_news(ticker, max_per_source)
        if alpha_articles:
            all_sources['alpha_vantage'] = alpha_articles
    except Exception as e:
        logger.warning(f"Failed to fetch Alpha Vantage news: {e}")
    
    return all_sources

def extract_headlines_from_sources(sources_data: Dict[str, List[Dict[str, Any]]]) -> List[str]:
    """
    Extract headlines/titles from all sources for sentiment analysis.
    
    Args:
        sources_data: Dictionary of source data
        
    Returns:
        List[str]: List of headlines/titles
    """
    headlines = []
    
    for source_name, articles in sources_data.items():
        for article in articles:
            if source_name == 'reddit':
                # For Reddit, use title and combine with text if available
                title = article.get('title', '')
                text = article.get('text', '')
                if title:
                    headlines.append(title)
                if text and len(text.strip()) > 20:  # Only include substantial text
                    headlines.append(text[:200])  # Limit length
            else:
                # For news sources, use title and description
                title = article.get('title', '')
                description = article.get('description', '') or article.get('summary', '')
                
                if title:
                    headlines.append(title)
                if description and len(description.strip()) > 20:
                    headlines.append(description[:200])
    
    # Remove duplicates while preserving order
    seen = set()
    unique_headlines = []
    for headline in headlines:
        if headline and headline not in seen:
            seen.add(headline)
            unique_headlines.append(headline)
    
    return unique_headlines

def get_social_media_status() -> Dict[str, Any]:
    """
    Check the status of social media news sources.
    
    Returns:
        Dict[str, Any]: Status information
    """
    status = {
        "reddit_accessible": False,
        "newsapi_configured": False,
        "newsapi_accessible": False,
        "alpha_vantage_configured": False,
        "alpha_vantage_accessible": False,
        "available_sources": [],
        "error_messages": []
    }
    
    # Test Reddit access
    try:
        test_posts = get_reddit_posts("AAPL", 1)
        status["reddit_accessible"] = True
        status["available_sources"].append("reddit")
    except Exception as e:
        status["error_messages"].append(f"Reddit error: {e}")
    
    # Check NewsAPI
    newsapi_key = os.getenv('NEWSAPI_KEY')
    if newsapi_key:
        status["newsapi_configured"] = True
        try:
            test_articles = get_newsapi_articles("AAPL", 1)
            status["newsapi_accessible"] = True
            status["available_sources"].append("newsapi")
        except Exception as e:
            status["error_messages"].append(f"NewsAPI error: {e}")
    
    # Check Alpha Vantage
    alpha_key = os.getenv('ALPHA_VANTAGE_API_KEY')
    if alpha_key:
        status["alpha_vantage_configured"] = True
        try:
            test_articles = get_alpha_vantage_news("AAPL", 1)
            status["alpha_vantage_accessible"] = True
            status["available_sources"].append("alpha_vantage")
        except Exception as e:
            status["error_messages"].append(f"Alpha Vantage error: {e}")
    
    return status
