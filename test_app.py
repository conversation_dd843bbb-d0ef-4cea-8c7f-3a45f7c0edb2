"""
Simple test suite for the streamlined stock scanner.
"""

import unittest
from unittest.mock import patch, Mock
import pandas as pd
from data_sources import (
    get_stock_data, analyze_sentiment_simple, calculate_sentiment_score,
    generate_recommendation, check_data_sources
)

class TestDataSources(unittest.TestCase):
    """Test the consolidated data sources module."""
    
    @patch('data_sources.yf.Ticker')
    def test_get_stock_data_success(self, mock_ticker):
        """Test successful stock data retrieval."""
        mock_stock = Mock()
        mock_stock.info = {
            'symbol': 'AAPL',
            'longName': 'Apple Inc.',
            'currentPrice': 150.0,
            'previousClose': 148.0,
            'marketCap': 2500000000000,
            'trailingPE': 25.0,
            'volume': 50000000,
            'sector': 'Technology'
        }
        mock_ticker.return_value = mock_stock
        
        result = get_stock_data('AAPL')
        
        self.assertEqual(result['symbol'], 'AAPL')
        self.assertEqual(result['current_price'], 150.0)
        self.assertAlmostEqual(result['price_change_percent'], 1.35, places=1)
    
    def test_analyze_sentiment_simple(self):
        """Test simple sentiment analysis."""
        # Positive sentiment
        positive_text = "Great stock with excellent growth potential"
        pos_score = analyze_sentiment_simple(positive_text)
        self.assertGreater(pos_score, 0)
        
        # Negative sentiment
        negative_text = "Terrible stock, major decline expected"
        neg_score = analyze_sentiment_simple(negative_text)
        self.assertLess(neg_score, 0)
        
        # Neutral sentiment
        neutral_text = "The company reported quarterly results"
        neutral_score = analyze_sentiment_simple(neutral_text)
        self.assertEqual(neutral_score, 0.0)
    
    def test_calculate_sentiment_score(self):
        """Test sentiment score calculation."""
        headlines = [
            "Stock rises on good news",
            "Excellent quarterly results",
            "Company shows strong growth"
        ]
        
        result = calculate_sentiment_score(headlines)
        
        self.assertIn('score', result)
        self.assertIn('count', result)
        self.assertIn('confidence', result)
        self.assertEqual(result['count'], 3)
        self.assertGreater(result['score'], 0)  # Should be positive
    
    def test_generate_recommendation(self):
        """Test recommendation generation."""
        stock_data = {
            'price_change_percent': 3.0,
            'pe_ratio': 15.0
        }
        sentiment_data = {
            'score': 0.3,
            'confidence': 'High'
        }
        
        result = generate_recommendation(stock_data, sentiment_data)
        
        self.assertIn('action', result)
        self.assertIn('score', result)
        self.assertIn('reasons', result)
        self.assertIn(result['action'], ['BUY', 'WEAK BUY', 'HOLD', 'WEAK SELL', 'SELL'])
    
    def test_check_data_sources(self):
        """Test data source availability check."""
        result = check_data_sources()
        
        self.assertIn('yfinance', result)
        self.assertIn('marketaux', result)
        self.assertIn('reddit', result)
        self.assertTrue(result['yfinance'])  # Should always be True
        self.assertTrue(result['reddit'])   # Should always be True

if __name__ == '__main__':
    unittest.main(verbosity=2)
