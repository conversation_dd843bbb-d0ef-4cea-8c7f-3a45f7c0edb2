# Enhanced Stock Scanner Configuration
# Copy this file to .env and fill in your API keys

# =============================================================================
# REQUIRED API KEYS
# =============================================================================

# Marketaux API Key (Required for traditional news)
# Get your free API key at: https://www.marketaux.com/
# Free tier: 100 requests/day
MARKETAUX_API_KEY=your_marketaux_api_key_here

# =============================================================================
# OPTIONAL API KEYS (for enhanced features)
# =============================================================================

# NewsAPI Key (Optional - for additional news sources)
# Get your free API key at: https://newsapi.org/
# Free tier: 1000 requests/day
# NEWSAPI_KEY=your_newsapi_key_here

# Alpha Vantage API Key (Optional - for additional financial news)
# Get your free API key at: https://www.alphavantage.co/
# Free tier: 25 requests/day
# ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# =============================================================================
# APPLICATION SETTINGS (Optional)
# =============================================================================

# Streamlit server port (default: 8501)
# STREAMLIT_SERVER_PORT=8501

# =============================================================================
# INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env (remove .example extension)
# 2. Replace "your_api_key_here" with your actual API keys
# 3. Remove the # symbol to uncomment optional keys you want to use
# 4. Save the file and restart the application

# =============================================================================
# API KEY SOURCES
# =============================================================================

# Marketaux (Required):
# - Website: https://www.marketaux.com/
# - Free tier: 100 requests/day
# - Provides: Financial news and market data

# NewsAPI (Optional):
# - Website: https://newsapi.org/
# - Free tier: 1000 requests/day
# - Provides: General news articles

# Alpha Vantage (Optional):
# - Website: https://www.alphavantage.co/
# - Free tier: 25 requests/day
# - Provides: Financial news with sentiment scores

# =============================================================================
# NOTES
# =============================================================================

# - The application will work with just the Marketaux API key
# - Additional API keys provide more news sources and better coverage
# - Reddit integration works without any API key
# - Stock data (yfinance) works without any API key
# - Keep your API keys secure and never commit them to version control
