"""
Configuration validation module for the Stock Scanner application.

This module provides functions to validate environment variables,
API configurations, and system dependencies.
"""

import os
import logging
from typing import Dict, Any, List, Tuple
from dotenv import load_dotenv
from news_fetch import get_api_status, APIKeyError
from sentiment_analyzer import get_sentiment_analyzer_status
from stock_data import get_stock_status
from social_media_news import get_social_media_status

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class ConfigurationError(Exception):
    """Exception raised for configuration-related errors."""
    pass

def validate_environment_variables() -> Dict[str, Any]:
    """
    Validate all required environment variables.
    
    Returns:
        Dict[str, Any]: Validation results with status and error messages
    """
    validation_results = {
        "valid": True,
        "errors": [],
        "warnings": [],
        "env_vars": {}
    }
    
    # Required environment variables
    required_vars = {
        "MARKETAUX_API_KEY": {
            "description": "API key for Marketaux news service",
            "min_length": 10,
            "required": True
        }
    }
    
    # Optional environment variables with defaults
    optional_vars = {
        "STREAMLIT_SERVER_PORT": {
            "description": "Port for Streamlit server",
            "default": "8501",
            "required": False
        },
        "NEWSAPI_KEY": {
            "description": "API key for NewsAPI service (optional)",
            "default": "",
            "required": False
        },
        "ALPHA_VANTAGE_API_KEY": {
            "description": "API key for Alpha Vantage service (optional)",
            "default": "",
            "required": False
        }
    }
    
    # Check required variables
    for var_name, config in required_vars.items():
        value = os.getenv(var_name)
        var_status = {
            "value": value,
            "configured": value is not None,
            "valid": False,
            "error": None
        }
        
        if not value:
            error_msg = f"Required environment variable '{var_name}' is not set. {config['description']}"
            validation_results["errors"].append(error_msg)
            validation_results["valid"] = False
            var_status["error"] = "Not configured"
        elif len(value.strip()) < config.get("min_length", 1):
            error_msg = f"Environment variable '{var_name}' is too short (minimum {config['min_length']} characters)"
            validation_results["errors"].append(error_msg)
            validation_results["valid"] = False
            var_status["error"] = "Too short"
        else:
            var_status["valid"] = True
        
        validation_results["env_vars"][var_name] = var_status
    
    # Check optional variables
    for var_name, config in optional_vars.items():
        value = os.getenv(var_name, config["default"])
        var_status = {
            "value": value,
            "configured": os.getenv(var_name) is not None,
            "valid": True,
            "default_used": os.getenv(var_name) is None
        }
        
        if not var_status["configured"]:
            validation_results["warnings"].append(
                f"Optional environment variable '{var_name}' not set, using default: {config['default']}"
            )
        
        validation_results["env_vars"][var_name] = var_status
    
    return validation_results

def validate_api_configuration() -> Dict[str, Any]:
    """
    Validate API configuration and connectivity.
    
    Returns:
        Dict[str, Any]: API validation results
    """
    logger.info("Validating API configuration...")
    
    try:
        api_status = get_api_status()
        
        validation_results = {
            "valid": api_status["api_accessible"],
            "api_key_configured": api_status["api_key_configured"],
            "api_key_valid": api_status["api_key_valid"],
            "api_accessible": api_status["api_accessible"],
            "error_message": api_status.get("error_message"),
            "recommendations": []
        }
        
        # Add recommendations based on status
        if not api_status["api_key_configured"]:
            validation_results["recommendations"].append(
                "Set MARKETAUX_API_KEY in your .env file"
            )
        elif not api_status["api_key_valid"]:
            validation_results["recommendations"].append(
                "Check that your MARKETAUX_API_KEY is correct and active"
            )
        elif not api_status["api_accessible"]:
            validation_results["recommendations"].append(
                "Check your internet connection and API service status"
            )
        
        return validation_results
        
    except Exception as e:
        logger.error(f"Error validating API configuration: {e}")
        return {
            "valid": False,
            "error_message": f"Failed to validate API configuration: {e}",
            "recommendations": ["Check your network connection and API configuration"]
        }

def validate_sentiment_analyzer() -> Dict[str, Any]:
    """
    Validate sentiment analyzer configuration and dependencies.
    
    Returns:
        Dict[str, Any]: Sentiment analyzer validation results
    """
    logger.info("Validating sentiment analyzer...")
    
    try:
        analyzer_status = get_sentiment_analyzer_status()
        
        validation_results = {
            "valid": analyzer_status["analyzer_functional"],
            "nltk_available": analyzer_status["nltk_available"],
            "vader_lexicon_available": analyzer_status["vader_lexicon_available"],
            "analyzer_functional": analyzer_status["analyzer_functional"],
            "error_message": analyzer_status.get("error_message"),
            "recommendations": []
        }
        
        # Add recommendations based on status
        if not analyzer_status["nltk_available"]:
            validation_results["recommendations"].append(
                "Install NLTK: pip install nltk"
            )
        elif not analyzer_status["vader_lexicon_available"]:
            validation_results["recommendations"].append(
                "VADER lexicon will be downloaded automatically on first use"
            )
        elif not analyzer_status["analyzer_functional"]:
            validation_results["recommendations"].append(
                "Check NLTK installation and internet connection for resource downloads"
            )
        
        return validation_results

    except Exception as e:
        logger.error(f"Error validating sentiment analyzer: {e}")
        return {
            "valid": False,
            "error_message": f"Failed to validate sentiment analyzer: {e}",
            "recommendations": ["Check NLTK installation and dependencies"]
        }

def validate_stock_data() -> Dict[str, Any]:
    """
    Validate stock data service configuration and dependencies.

    Returns:
        Dict[str, Any]: Stock data validation results
    """
    logger.info("Validating stock data service...")

    try:
        stock_status = get_stock_status()

        validation_results = {
            "valid": stock_status["test_ticker_accessible"],
            "yfinance_available": stock_status["yfinance_available"],
            "test_ticker_accessible": stock_status["test_ticker_accessible"],
            "error_message": stock_status.get("error_message"),
            "recommendations": []
        }

        # Add recommendations based on status
        if not stock_status["yfinance_available"]:
            validation_results["recommendations"].append(
                "Install yfinance: pip install yfinance"
            )
        elif not stock_status["test_ticker_accessible"]:
            validation_results["recommendations"].append(
                "Check internet connection for stock data access"
            )

        return validation_results

    except Exception as e:
        logger.error(f"Error validating stock data service: {e}")
        return {
            "valid": False,
            "error_message": f"Failed to validate stock data service: {e}",
            "recommendations": ["Check yfinance installation and internet connection"]
        }

def validate_social_media_news() -> Dict[str, Any]:
    """
    Validate social media news sources configuration.

    Returns:
        Dict[str, Any]: Social media news validation results
    """
    logger.info("Validating social media news sources...")

    try:
        social_status = get_social_media_status()

        validation_results = {
            "valid": len(social_status["available_sources"]) > 0,
            "reddit_accessible": social_status["reddit_accessible"],
            "newsapi_configured": social_status["newsapi_configured"],
            "newsapi_accessible": social_status["newsapi_accessible"],
            "alpha_vantage_configured": social_status["alpha_vantage_configured"],
            "alpha_vantage_accessible": social_status["alpha_vantage_accessible"],
            "available_sources": social_status["available_sources"],
            "error_messages": social_status["error_messages"],
            "recommendations": []
        }

        # Add recommendations based on status
        if not social_status["reddit_accessible"]:
            validation_results["recommendations"].append(
                "Reddit access failed - check internet connection"
            )

        if not social_status["newsapi_configured"]:
            validation_results["recommendations"].append(
                "Optional: Set NEWSAPI_KEY for additional news sources"
            )

        if not social_status["alpha_vantage_configured"]:
            validation_results["recommendations"].append(
                "Optional: Set ALPHA_VANTAGE_API_KEY for additional news sources"
            )

        return validation_results

    except Exception as e:
        logger.error(f"Error validating social media news: {e}")
        return {
            "valid": False,
            "error_message": f"Failed to validate social media news: {e}",
            "recommendations": ["Check internet connection and API configurations"]
        }

def run_full_configuration_check() -> Dict[str, Any]:
    """
    Run a comprehensive configuration check for the entire application.
    
    Returns:
        Dict[str, Any]: Complete validation results
    """
    logger.info("Running full configuration check...")
    
    results = {
        "overall_valid": True,
        "environment": validate_environment_variables(),
        "api": validate_api_configuration(),
        "sentiment_analyzer": validate_sentiment_analyzer(),
        "stock_data": validate_stock_data(),
        "social_media_news": validate_social_media_news(),
        "summary": {
            "total_errors": 0,
            "total_warnings": 0,
            "critical_issues": [],
            "recommendations": []
        }
    }
    
    # Collect all errors and warnings
    all_errors = []
    all_warnings = []
    
    # Environment validation
    if not results["environment"]["valid"]:
        results["overall_valid"] = False
        all_errors.extend(results["environment"]["errors"])
    all_warnings.extend(results["environment"]["warnings"])
    
    # API validation
    if not results["api"]["valid"]:
        results["overall_valid"] = False
        if results["api"].get("error_message"):
            all_errors.append(f"API Error: {results['api']['error_message']}")
    
    # Sentiment analyzer validation
    if not results["sentiment_analyzer"]["valid"]:
        # Sentiment analyzer issues are not critical for basic functionality
        if results["sentiment_analyzer"].get("error_message"):
            all_warnings.append(f"Sentiment Analyzer Warning: {results['sentiment_analyzer']['error_message']}")

    # Stock data validation
    if not results["stock_data"]["valid"]:
        # Stock data issues are not critical but important for enhanced functionality
        if results["stock_data"].get("error_message"):
            all_warnings.append(f"Stock Data Warning: {results['stock_data']['error_message']}")

    # Social media news validation
    if not results["social_media_news"]["valid"]:
        # Social media news issues are not critical
        if results["social_media_news"].get("error_messages"):
            for error_msg in results["social_media_news"]["error_messages"]:
                all_warnings.append(f"Social Media News Warning: {error_msg}")

    # Update summary
    results["summary"]["total_errors"] = len(all_errors)
    results["summary"]["total_warnings"] = len(all_warnings)
    results["summary"]["critical_issues"] = all_errors

    # Collect recommendations
    for component in ["api", "sentiment_analyzer", "stock_data", "social_media_news"]:
        if "recommendations" in results[component]:
            results["summary"]["recommendations"].extend(results[component]["recommendations"])
    
    logger.info(f"Configuration check complete. Valid: {results['overall_valid']}, "
                f"Errors: {len(all_errors)}, Warnings: {len(all_warnings)}")
    
    return results

def get_configuration_help() -> str:
    """
    Get help text for configuration setup.
    
    Returns:
        str: Formatted help text
    """
    help_text = """
    📋 Enhanced Stock Scanner Configuration Help

    🔧 Required Setup:
    1. Create a .env file in your project root
    2. Add your Marketaux API key: MARKETAUX_API_KEY=your_api_key_here
    3. Get a free API key from: https://www.marketaux.com/

    🔧 Optional Setup (for enhanced features):
    4. NewsAPI key: NEWSAPI_KEY=your_newsapi_key (from https://newsapi.org/)
    5. Alpha Vantage key: ALPHA_VANTAGE_API_KEY=your_key (from https://www.alphavantage.co/)

    📦 Dependencies:
    - streamlit: Web interface framework
    - requests: HTTP client for API calls
    - nltk: Natural language processing
    - python-dotenv: Environment variable management
    - yfinance: Stock data and financial metrics
    - plotly: Interactive charts and visualizations
    - pandas: Data manipulation and analysis

    🚀 Quick Start:
    1. pip install yfinance plotly (core dependencies)
    2. Set up your .env file with required API key
    3. Run: streamlit run stock_scanner_app.py

    ✨ New Features:
    - Real-time stock prices and financial metrics via yfinance
    - Social media sentiment from Reddit (no API key needed)
    - Additional news sources with optional API keys
    - Interactive charts and technical indicators

    ❓ Troubleshooting:
    - API errors: Check your API keys and internet connection
    - NLTK errors: VADER lexicon will download automatically
    - Import errors: Install missing dependencies with pip
    - Stock data errors: Check internet connection for yfinance
    """
    
    return help_text.strip()
