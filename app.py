"""
Streamlined Stock Scanner with yfinance and Social Media Integration.
A simplified, compact version of the enhanced stock scanner.
"""

import streamlit as st
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
from data_sources import (
    get_stock_data, get_stock_chart_data, get_all_news, 
    calculate_sentiment_score, generate_recommendation, check_data_sources
)

# Page configuration
st.set_page_config(
    page_title="Enhanced Stock Scanner",
    page_icon="📈",
    layout="wide"
)

def create_stock_chart(ticker: str, period: str = "3mo"):
    """Create interactive stock chart."""
    hist_data = get_stock_chart_data(ticker, period)
    
    if hist_data is None or hist_data.empty:
        return None
    
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=(f'{ticker} Stock Price', 'Volume'),
        row_heights=[0.7, 0.3]
    )
    
    # Candlestick chart
    fig.add_trace(
        go.Candlestick(
            x=hist_data.index,
            open=hist_data['Open'],
            high=hist_data['High'],
            low=hist_data['Low'],
            close=hist_data['Close'],
            name='Price'
        ),
        row=1, col=1
    )
    
    # Moving averages
    if 'sma_20' in hist_data.columns:
        fig.add_trace(
            go.Scatter(
                x=hist_data.index,
                y=hist_data['sma_20'],
                mode='lines',
                name='SMA 20',
                line=dict(color='orange', width=1)
            ),
            row=1, col=1
        )
    
    # Volume
    fig.add_trace(
        go.Bar(
            x=hist_data.index,
            y=hist_data['Volume'],
            name='Volume',
            marker_color='lightblue'
        ),
        row=2, col=1
    )
    
    fig.update_layout(
        title=f'{ticker} Analysis',
        xaxis_rangeslider_visible=False,
        height=600
    )
    
    return fig

def display_stock_analysis(ticker: str, show_chart: bool = True):
    """Display comprehensive stock analysis."""
    # Get stock data
    stock_data = get_stock_data(ticker)
    
    if 'error' in stock_data:
        st.error(f"❌ Error fetching data for {ticker}: {stock_data['error']}")
        return
    
    # Get news and sentiment
    all_news = get_all_news(ticker)
    all_headlines = []
    for source_headlines in all_news.values():
        all_headlines.extend(source_headlines)
    
    sentiment_data = calculate_sentiment_score(all_headlines)
    recommendation = generate_recommendation(stock_data, sentiment_data)
    
    # Create expandable section
    with st.expander(
        f"📊 {ticker} - ${stock_data['current_price']:.2f} "
        f"({recommendation['color']} {recommendation['action']})", 
        expanded=True
    ):
        
        # Stock metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Current Price", 
                f"${stock_data['current_price']:.2f}",
                delta=f"{stock_data['price_change_percent']:+.1f}%"
            )
        
        with col2:
            market_cap = stock_data['market_cap']
            if market_cap > 1e9:
                cap_display = f"${market_cap/1e9:.1f}B"
            elif market_cap > 1e6:
                cap_display = f"${market_cap/1e6:.1f}M"
            else:
                cap_display = f"${market_cap:,.0f}"
            st.metric("Market Cap", cap_display)
        
        with col3:
            pe_ratio = stock_data['pe_ratio']
            st.metric("P/E Ratio", f"{pe_ratio:.1f}" if pe_ratio else "N/A")
        
        with col4:
            st.metric("Volume", f"{stock_data['volume']:,}")
        
        # Company info
        st.write(f"**Company:** {stock_data['company_name']}")
        st.write(f"**Sector:** {stock_data['sector']}")
        
        # Sentiment analysis
        st.subheader("📊 Sentiment Analysis")
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Sentiment Score", f"{sentiment_data['score']:.3f}")
        with col2:
            st.metric("Headlines Analyzed", sentiment_data['count'])
        with col3:
            st.metric("Confidence", sentiment_data['confidence'])
        
        # News breakdown
        if all_news['traditional'] or all_news['reddit'] or all_news['newsapi']:
            st.write("**News Sources:**")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write(f"Traditional: {len(all_news['traditional'])}")
            with col2:
                st.write(f"Reddit: {len(all_news['reddit'])}")
            with col3:
                st.write(f"NewsAPI: {len(all_news['newsapi'])}")
        
        # Trading recommendation
        st.subheader("💡 Trading Recommendation")
        
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Recommendation", f"{recommendation['color']} {recommendation['action']}")
        with col2:
            st.metric("Confidence", recommendation['confidence'])
        
        st.write("**Analysis:**")
        for reason in recommendation['reasons']:
            st.write(f"• {reason}")
        
        # Chart
        if show_chart:
            st.subheader("📈 Price Chart")
            chart = create_stock_chart(ticker)
            if chart:
                st.plotly_chart(chart, use_container_width=True)
        
        # Recent headlines
        if all_headlines:
            st.subheader("📰 Recent Headlines")
            for i, headline in enumerate(all_headlines[:5], 1):
                st.write(f"{i}. {headline}")
        
        # Disclaimer
        st.info("⚠️ **Disclaimer:** This is for educational purposes only. Not financial advice.")

def main():
    """Main application."""
    st.title("📈 Enhanced Stock Scanner")
    st.write("Real-time stock analysis with sentiment from news and social media")
    
    # Sidebar configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # Data source status
        sources = check_data_sources()
        st.write("**Data Sources:**")
        for source, available in sources.items():
            status = "✅" if available else "❌"
            st.write(f"{status} {source.title()}")
        
        st.header("📊 Analysis Options")
        show_charts = st.checkbox("Show Price Charts", value=True)
        chart_period = st.selectbox("Chart Period", ["1mo", "3mo", "6mo", "1y"], index=1)
        
        # API key setup info
        if not sources['marketaux']:
            st.warning("⚠️ Set MARKETAUX_API_KEY in .env for news data")
        
        if not sources['newsapi']:
            st.info("💡 Set NEWSAPI_KEY in .env for additional news")
    
    # Main input
    st.header("🎯 Stock Analysis")
    
    # Stock input
    tickers_input = st.text_input(
        "Enter stock tickers (comma-separated):",
        placeholder="AAPL, TSLA, GOOGL",
        help="Enter one or more stock ticker symbols"
    )
    
    # Analysis button
    if st.button("🔍 Analyze Stocks", type="primary"):
        if not tickers_input.strip():
            st.error("Please enter at least one stock ticker")
            return
        
        # Parse tickers
        tickers = [ticker.strip().upper() for ticker in tickers_input.split(",")]
        tickers = [t for t in tickers if t]  # Remove empty strings
        
        if not tickers:
            st.error("Please enter valid stock tickers")
            return
        
        st.success(f"Analyzing {len(tickers)} stock(s)...")
        
        # Progress bar
        progress_bar = st.progress(0)
        
        # Analyze each stock
        for i, ticker in enumerate(tickers):
            progress_bar.progress((i + 1) / len(tickers))
            display_stock_analysis(ticker, show_charts)
        
        progress_bar.empty()
        st.success("✅ Analysis complete!")
    
    # Quick examples
    st.header("💡 Quick Examples")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📱 Tech Stocks"):
            st.session_state.example_tickers = "AAPL, GOOGL, MSFT"
    
    with col2:
        if st.button("🚗 Auto Stocks"):
            st.session_state.example_tickers = "TSLA, F, GM"
    
    with col3:
        if st.button("🏦 Bank Stocks"):
            st.session_state.example_tickers = "JPM, BAC, WFC"
    
    # Handle example button clicks
    if hasattr(st.session_state, 'example_tickers'):
        st.rerun()

if __name__ == "__main__":
    main()
