import streamlit as st
import re
import logging
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any
from news_fetch import fetch_stock_news_marketaux, NewsAPIError, APIKeyError, RateLimitError
from sentiment_analyzer import analyze_sentiment_safe, SentimentAnalysisError
from config_validator import run_full_configuration_check, get_configuration_help
from stock_data import get_stock_info, get_historical_data, calculate_technical_indicators, StockDataError
from social_media_news import get_free_news_sources, extract_headlines_from_sources

POSITIVE_SENTIMENT_THRESHOLD = 0.2  # Define your positive sentiment threshold

# Configure logging
logger = logging.getLogger(__name__)

# Input validation constants
MAX_TICKERS = 10  # Maximum number of tickers to process at once
MAX_TICKER_LENGTH = 10  # Maximum length for a single ticker symbol
MIN_TICKER_LENGTH = 1  # Minimum length for a single ticker symbol

st.title("Sentiment-Driven Stock Scanner")

# Configuration and system status
@st.cache_data(ttl=300)  # Cache for 5 minutes
def check_system_configuration():
    """Check system configuration and return status."""
    return run_full_configuration_check()

# Display system status in sidebar
with st.sidebar:
    st.header("System Status")

    if st.button("🔄 Check Configuration"):
        st.cache_data.clear()  # Clear cache to force recheck

    config_status = check_system_configuration()

    if config_status["overall_valid"]:
        st.success("✅ System Ready")
    else:
        st.error("❌ Configuration Issues")

        if config_status["summary"]["critical_issues"]:
            st.subheader("Critical Issues:")
            for issue in config_status["summary"]["critical_issues"]:
                st.error(f"• {issue}")

        if config_status["summary"]["recommendations"]:
            st.subheader("Recommendations:")
            for rec in config_status["summary"]["recommendations"]:
                st.info(f"• {rec}")

    # Show warnings if any
    if config_status["summary"]["total_warnings"] > 0:
        with st.expander("⚠️ Warnings"):
            if config_status["environment"]["warnings"]:
                for warning in config_status["environment"]["warnings"]:
                    st.warning(warning)

    # Configuration help
    with st.expander("📋 Setup Help"):
        st.text(get_configuration_help())

    st.header("Analysis Options")
    enable_historical = st.checkbox("Enable Historical Tracking", value=True)
    if enable_historical:
        days_to_analyze = st.slider("Days to Analyze", 1, 30, 7)

    st.header("Enhanced Features")
    enable_stock_data = st.checkbox("Enable Stock Data & Metrics", value=True, help="Fetch real-time stock prices and financial metrics")
    enable_social_media = st.checkbox("Enable Social Media News", value=True, help="Include Reddit and other social media sources")
    enable_charts = st.checkbox("Enable Interactive Charts", value=True, help="Show price charts and technical indicators")

    if enable_charts:
        chart_period = st.selectbox("Chart Period", ["1mo", "3mo", "6mo", "1y", "2y"], index=1)

def validate_ticker_format(ticker: str) -> bool:
    """
    Validate if a ticker symbol has the correct format.

    Args:
        ticker: The ticker symbol to validate

    Returns:
        bool: True if ticker format is valid, False otherwise
    """
    if not ticker or not isinstance(ticker, str):
        return False

    # Remove whitespace and convert to uppercase
    ticker = ticker.strip().upper()

    # Check length constraints
    if len(ticker) < MIN_TICKER_LENGTH or len(ticker) > MAX_TICKER_LENGTH:
        return False

    # Check if ticker contains only valid characters (letters, numbers, dots, and hyphens)
    # Most stock tickers are alphanumeric, but some may contain dots or hyphens
    # Exclude special characters like @, $, %, etc.
    if not re.match(r'^[A-Z0-9.-]+$', ticker):
        return False

    # Additional check: ticker should start with a letter (not just numbers or special chars)
    if not re.match(r'^[A-Z]', ticker):
        return False

    return True

def sanitize_and_validate_tickers(input_string: str) -> Tuple[List[str], List[str]]:
    """
    Sanitize and validate a comma-separated string of ticker symbols.

    Args:
        input_string: Comma-separated ticker symbols

    Returns:
        Tuple[List[str], List[str]]: (valid_tickers, error_messages)
    """
    if not input_string or not input_string.strip():
        return [], ["Please enter at least one stock ticker."]

    # Split by comma and clean up
    raw_tickers = [ticker.strip().upper() for ticker in input_string.split(',')]

    # Remove empty strings
    raw_tickers = [ticker for ticker in raw_tickers if ticker]

    valid_tickers = []
    error_messages = []

    # Check maximum number of tickers
    if len(raw_tickers) > MAX_TICKERS:
        error_messages.append(f"Too many tickers. Maximum allowed: {MAX_TICKERS}. You entered: {len(raw_tickers)}")
        return [], error_messages

    # Validate each ticker
    for ticker in raw_tickers:
        if validate_ticker_format(ticker):
            if ticker not in valid_tickers:  # Avoid duplicates
                valid_tickers.append(ticker)
            else:
                error_messages.append(f"Duplicate ticker removed: {ticker}")
        else:
            if len(ticker) < MIN_TICKER_LENGTH:
                error_messages.append(f"Ticker '{ticker}' is too short (minimum {MIN_TICKER_LENGTH} characters)")
            elif len(ticker) > MAX_TICKER_LENGTH:
                error_messages.append(f"Ticker '{ticker}' is too long (maximum {MAX_TICKER_LENGTH} characters)")
            else:
                error_messages.append(f"Invalid ticker format: '{ticker}'. Use only letters, numbers, dots, and hyphens.")

    if not valid_tickers and not error_messages:
        error_messages.append("No valid ticker symbols found.")

    return valid_tickers, error_messages

def get_sentiment_label(score):
    if score > POSITIVE_SENTIMENT_THRESHOLD:
        return "Good"
    elif score < -POSITIVE_SENTIMENT_THRESHOLD:
        return "Bad"
    else:
        return "Neutral"

def calculate_weighted_sentiment(traditional_headlines, social_headlines, traditional_weight=0.7, social_weight=0.3):
    """
    Calculate weighted sentiment score combining traditional news and social media.

    Args:
        traditional_headlines: List of traditional news headlines
        social_headlines: List of social media headlines
        traditional_weight: Weight for traditional news (default 0.7)
        social_weight: Weight for social media (default 0.3)

    Returns:
        Dict containing weighted sentiment analysis results
    """
    results = {
        "traditional_sentiment": 0.0,
        "social_sentiment": 0.0,
        "weighted_sentiment": 0.0,
        "traditional_count": len(traditional_headlines),
        "social_count": len(social_headlines),
        "total_count": len(traditional_headlines) + len(social_headlines),
        "sentiment_breakdown": {
            "traditional_scores": [],
            "social_scores": [],
            "combined_scores": []
        }
    }

    # Analyze traditional news sentiment
    traditional_scores = []
    for headline in traditional_headlines:
        try:
            score = analyze_sentiment_safe(headline, default_score=0.0)
            traditional_scores.append(score)
        except Exception:
            traditional_scores.append(0.0)

    # Analyze social media sentiment
    social_scores = []
    for headline in social_headlines:
        try:
            score = analyze_sentiment_safe(headline, default_score=0.0)
            social_scores.append(score)
        except Exception:
            social_scores.append(0.0)

    # Calculate average sentiments
    if traditional_scores:
        results["traditional_sentiment"] = sum(traditional_scores) / len(traditional_scores)
        results["sentiment_breakdown"]["traditional_scores"] = traditional_scores

    if social_scores:
        results["social_sentiment"] = sum(social_scores) / len(social_scores)
        results["sentiment_breakdown"]["social_scores"] = social_scores

    # Calculate weighted sentiment
    if traditional_scores or social_scores:
        if traditional_scores and social_scores:
            # Both sources available - use weighted average
            results["weighted_sentiment"] = (
                results["traditional_sentiment"] * traditional_weight +
                results["social_sentiment"] * social_weight
            )
        elif traditional_scores:
            # Only traditional news available
            results["weighted_sentiment"] = results["traditional_sentiment"]
        else:
            # Only social media available
            results["weighted_sentiment"] = results["social_sentiment"]

        # Store all scores for analysis
        all_scores = traditional_scores + social_scores
        results["sentiment_breakdown"]["combined_scores"] = all_scores

    return results

def get_sentiment_confidence(scores_list):
    """
    Calculate confidence level for sentiment analysis based on score distribution.

    Args:
        scores_list: List of sentiment scores

    Returns:
        Dict with confidence metrics
    """
    if not scores_list:
        return {"confidence": "No Data", "consistency": 0.0, "volatility": 0.0}

    scores_array = pd.Series(scores_list)

    # Calculate metrics
    mean_score = scores_array.mean()
    std_score = scores_array.std()
    consistency = 1 - (std_score / 2.0) if std_score <= 2.0 else 0.0  # Normalize to 0-1

    # Determine confidence level
    if len(scores_list) >= 10 and consistency >= 0.7:
        confidence = "High"
    elif len(scores_list) >= 5 and consistency >= 0.5:
        confidence = "Medium"
    elif len(scores_list) >= 3:
        confidence = "Low"
    else:
        confidence = "Very Low"

    return {
        "confidence": confidence,
        "consistency": consistency,
        "volatility": std_score,
        "sample_size": len(scores_list)
    }

def generate_trading_recommendation(stock_data, sentiment_data, confidence_metrics):
    """
    Generate trading recommendations based on stock data, sentiment, and confidence.

    Args:
        stock_data: Dictionary containing stock information
        sentiment_data: Dictionary containing sentiment analysis results
        confidence_metrics: Dictionary containing confidence metrics

    Returns:
        Dict containing recommendation and reasoning
    """
    recommendation = {
        "action": "HOLD",
        "confidence": "Medium",
        "reasoning": [],
        "risk_level": "Medium",
        "score": 0.0  # -1 to 1 scale
    }

    score = 0.0
    reasoning = []

    # Sentiment analysis factors (40% weight)
    sentiment_score = sentiment_data.get('weighted_sentiment', 0)
    sentiment_weight = 0.4

    if sentiment_score > 0.3:
        score += 0.4 * sentiment_weight
        reasoning.append(f"Strong positive sentiment ({sentiment_score:.3f})")
    elif sentiment_score > 0.1:
        score += 0.2 * sentiment_weight
        reasoning.append(f"Moderate positive sentiment ({sentiment_score:.3f})")
    elif sentiment_score < -0.3:
        score -= 0.4 * sentiment_weight
        reasoning.append(f"Strong negative sentiment ({sentiment_score:.3f})")
    elif sentiment_score < -0.1:
        score -= 0.2 * sentiment_weight
        reasoning.append(f"Moderate negative sentiment ({sentiment_score:.3f})")
    else:
        reasoning.append(f"Neutral sentiment ({sentiment_score:.3f})")

    # Price momentum factors (30% weight)
    if 'price_change_percent' in stock_data:
        price_change = stock_data['price_change_percent']
        momentum_weight = 0.3

        if price_change > 5:
            score += 0.3 * momentum_weight
            reasoning.append(f"Strong positive momentum (+{price_change:.1f}%)")
        elif price_change > 2:
            score += 0.15 * momentum_weight
            reasoning.append(f"Positive momentum (+{price_change:.1f}%)")
        elif price_change < -5:
            score -= 0.3 * momentum_weight
            reasoning.append(f"Strong negative momentum ({price_change:.1f}%)")
        elif price_change < -2:
            score -= 0.15 * momentum_weight
            reasoning.append(f"Negative momentum ({price_change:.1f}%)")

    # Valuation factors (20% weight)
    if 'pe_ratio' in stock_data and stock_data['pe_ratio'] > 0:
        pe_ratio = stock_data['pe_ratio']
        valuation_weight = 0.2

        if pe_ratio < 15:
            score += 0.2 * valuation_weight
            reasoning.append(f"Attractive valuation (P/E: {pe_ratio:.1f})")
        elif pe_ratio > 30:
            score -= 0.2 * valuation_weight
            reasoning.append(f"High valuation (P/E: {pe_ratio:.1f})")

    # Confidence adjustment (10% weight)
    conf_level = confidence_metrics.get('confidence', 'Medium')
    if conf_level == 'High':
        score += 0.1
        reasoning.append("High confidence in sentiment analysis")
    elif conf_level == 'Low' or conf_level == 'Very Low':
        score -= 0.1
        reasoning.append("Low confidence in sentiment analysis")

    # Determine recommendation
    recommendation['score'] = score

    if score > 0.3:
        recommendation['action'] = "BUY"
        recommendation['confidence'] = "High" if score > 0.5 else "Medium"
        recommendation['risk_level'] = "Medium"
    elif score > 0.1:
        recommendation['action'] = "WEAK BUY"
        recommendation['confidence'] = "Medium"
        recommendation['risk_level'] = "Medium"
    elif score < -0.3:
        recommendation['action'] = "SELL"
        recommendation['confidence'] = "High" if score < -0.5 else "Medium"
        recommendation['risk_level'] = "High"
    elif score < -0.1:
        recommendation['action'] = "WEAK SELL"
        recommendation['confidence'] = "Medium"
        recommendation['risk_level'] = "Medium-High"
    else:
        recommendation['action'] = "HOLD"
        recommendation['confidence'] = "Medium"
        recommendation['risk_level'] = "Medium"

    recommendation['reasoning'] = reasoning

    return recommendation

def plot_sentiment_history(historical_data, ticker):
    """
    Creates a matplotlib figure with sentiment history visualization.
    
    Args:
        historical_data: Dictionary mapping dates to sentiment scores
        ticker: Stock ticker symbol
        
    Returns:
        matplotlib figure
    """
    dates = list(historical_data.keys())
    values = list(historical_data.values())
    
    # Create figure and axis
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Plot the sentiment line
    ax.plot(dates, values, marker='o', linestyle='-', linewidth=2, markersize=8)
    
    # Add a horizontal line at neutral sentiment
    ax.axhline(y=0, color='gray', linestyle='--', alpha=0.7)
    
    # Color regions
    ax.axhspan(POSITIVE_SENTIMENT_THRESHOLD, 1, alpha=0.2, color='green')
    ax.axhspan(-POSITIVE_SENTIMENT_THRESHOLD, POSITIVE_SENTIMENT_THRESHOLD, alpha=0.2, color='gray')
    ax.axhspan(-1, -POSITIVE_SENTIMENT_THRESHOLD, alpha=0.2, color='red')
    
    # Add labels and title
    ax.set_xlabel('Date')
    ax.set_ylabel('Sentiment Score')
    ax.set_title(f'Sentiment History for {ticker}')
    
    # Customize grid
    ax.grid(True, alpha=0.3)
    
    # Rotate date labels for better readability
    plt.xticks(rotation=45)
    
    # Tight layout
    plt.tight_layout()
    
    return fig

def create_stock_chart(ticker: str, period: str = "3mo") -> go.Figure:
    """
    Create an interactive stock price chart with technical indicators.

    Args:
        ticker: Stock ticker symbol
        period: Time period for the chart

    Returns:
        plotly.graph_objects.Figure: Interactive stock chart
    """
    try:
        # Get historical data
        hist_data = get_historical_data(ticker, period=period)

        if hist_data.empty:
            return None

        # Calculate technical indicators
        hist_data = calculate_technical_indicators(hist_data)

        # Create subplots
        fig = make_subplots(
            rows=3, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=(f'{ticker} Stock Price', 'Volume', 'RSI'),
            row_heights=[0.6, 0.2, 0.2]
        )

        # Add candlestick chart
        fig.add_trace(
            go.Candlestick(
                x=hist_data.index,
                open=hist_data['open'],
                high=hist_data['high'],
                low=hist_data['low'],
                close=hist_data['close'],
                name='Price'
            ),
            row=1, col=1
        )

        # Add moving averages if available
        if 'sma_20' in hist_data.columns:
            fig.add_trace(
                go.Scatter(
                    x=hist_data.index,
                    y=hist_data['sma_20'],
                    mode='lines',
                    name='SMA 20',
                    line=dict(color='orange', width=1)
                ),
                row=1, col=1
            )

        if 'sma_50' in hist_data.columns:
            fig.add_trace(
                go.Scatter(
                    x=hist_data.index,
                    y=hist_data['sma_50'],
                    mode='lines',
                    name='SMA 50',
                    line=dict(color='blue', width=1)
                ),
                row=1, col=1
            )

        # Add volume
        fig.add_trace(
            go.Bar(
                x=hist_data.index,
                y=hist_data['volume'],
                name='Volume',
                marker_color='lightblue'
            ),
            row=2, col=1
        )

        # Add RSI if available
        if 'rsi' in hist_data.columns:
            fig.add_trace(
                go.Scatter(
                    x=hist_data.index,
                    y=hist_data['rsi'],
                    mode='lines',
                    name='RSI',
                    line=dict(color='purple')
                ),
                row=3, col=1
            )

            # Add RSI reference lines
            fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
            fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)

        # Update layout
        fig.update_layout(
            title=f'{ticker} Stock Analysis',
            xaxis_rangeslider_visible=False,
            height=800,
            showlegend=True
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating chart for {ticker}: {e}")
        return None

stock_tickers_input = st.text_input(
    "Enter Stock Tickers (comma-separated, e.g., AAPL,TSLA,GOOGL):", ""
).strip()

# Add helpful information about ticker format
with st.expander("ℹ️ Ticker Format Guidelines"):
    st.write(f"""
    - Use standard stock ticker symbols (e.g., AAPL, TSLA, GOOGL)
    - Separate multiple tickers with commas
    - Maximum {MAX_TICKERS} tickers per scan
    - Ticker length: {MIN_TICKER_LENGTH}-{MAX_TICKER_LENGTH} characters
    - Only letters, numbers, dots, and hyphens allowed
    """)

scan_button = st.button("Scan Stocks")

if scan_button:
    # Validate and sanitize input
    valid_tickers, validation_errors = sanitize_and_validate_tickers(stock_tickers_input)

    # Display validation errors if any
    if validation_errors:
        for error in validation_errors:
            st.error(error)

    # Proceed only if we have valid tickers and system is properly configured
    if valid_tickers:
        # Check if system is ready before proceeding
        if not config_status["overall_valid"]:
            st.error("❌ Cannot proceed: System configuration issues detected. Please check the sidebar for details.")
            st.stop()

        st.info(f"Scanning sentiment for {len(valid_tickers)} ticker(s): {', '.join(valid_tickers)}")
        stocks_sentiment_results = []
        historical_data = {}
        stock_data_results = {}
        social_media_data = {}
        failed_tickers = []

        # Create progress bar
        progress_bar = st.progress(0)
        status_text = st.empty()

        total_tickers = len(valid_tickers)

        for i, ticker in enumerate(valid_tickers):
            try:
                # Update progress
                progress = (i + 1) / total_tickers
                progress_bar.progress(progress)
                status_text.text(f"Processing {ticker}... ({i + 1}/{total_tickers})")

                # Fetch stock data if enabled
                if enable_stock_data:
                    try:
                        stock_info = get_stock_info(ticker)
                        stock_data_results[ticker] = stock_info
                        logger.info(f"Successfully fetched stock data for {ticker}")
                    except StockDataError as e:
                        logger.warning(f"Stock data error for {ticker}: {e}")
                        stock_data_results[ticker] = {"error": str(e)}
                    except Exception as e:
                        logger.warning(f"Unexpected stock data error for {ticker}: {e}")
                        stock_data_results[ticker] = {"error": f"Unexpected error: {e}"}

                # Fetch social media news if enabled
                all_headlines = []
                if enable_social_media:
                    try:
                        social_sources = get_free_news_sources(ticker, max_per_source=5)
                        social_media_data[ticker] = social_sources
                        social_headlines = extract_headlines_from_sources(social_sources)
                        all_headlines.extend(social_headlines)
                        logger.info(f"Fetched {len(social_headlines)} social media headlines for {ticker}")
                    except Exception as e:
                        logger.warning(f"Social media news error for {ticker}: {e}")
                        social_media_data[ticker] = {"error": str(e)}

                # Fetch news with error handling
                news_headlines = []
                try:
                    if enable_historical:
                        # Fetch with dates for historical tracking
                        news_data = fetch_stock_news_marketaux(
                            [ticker],
                            days_back=days_to_analyze,
                            return_dates=True
                        )

                        # Extract current headlines (all dates combined)
                        for date_headlines in news_data.values():
                            news_headlines.extend(date_headlines)

                        # Store historical data for later
                        historical_data[ticker] = news_data
                    else:
                        # Regular news fetch without dates
                        news_headlines = fetch_stock_news_marketaux([ticker])

                except APIKeyError as e:
                    st.error(f"🔑 API Key Error: {e}")
                    st.error("Please check your MARKETAUX_API_KEY in the .env file.")
                    st.stop()
                except RateLimitError as e:
                    st.error(f"⏱️ Rate Limit Error: {e}")
                    st.error("Please wait a few minutes before trying again.")
                    st.stop()
                except NewsAPIError as e:
                    st.warning(f"⚠️ News API Error for {ticker}: {e}")
                    failed_tickers.append({"ticker": ticker, "error": str(e)})
                    continue
                except Exception as e:
                    st.warning(f"⚠️ Unexpected error fetching news for {ticker}: {e}")
                    failed_tickers.append({"ticker": ticker, "error": f"Unexpected error: {e}"})
                    continue

                # Combine traditional news with social media headlines
                if news_headlines:
                    all_headlines.extend(news_headlines)

                if all_headlines:
                    # Calculate weighted sentiment analysis
                    social_headlines_only = all_headlines[len(news_headlines):] if news_headlines else all_headlines

                    if enable_social_media and social_headlines_only:
                        # Use weighted sentiment calculation
                        weighted_results = calculate_weighted_sentiment(
                            news_headlines,
                            social_headlines_only,
                            traditional_weight=0.7,
                            social_weight=0.3
                        )

                        average_sentiment = weighted_results["weighted_sentiment"]
                        sentiment_breakdown = weighted_results
                    else:
                        # Traditional sentiment analysis only
                        stock_sentiment_scores = []
                        sentiment_errors = 0

                        for headline in all_headlines:
                            try:
                                sentiment_score = analyze_sentiment_safe(headline, default_score=0.0)
                                stock_sentiment_scores.append(sentiment_score)
                            except Exception as e:
                                sentiment_errors += 1
                                logging.warning(f"Sentiment analysis failed for headline: {e}")
                                stock_sentiment_scores.append(0.0)

                        if sentiment_errors > 0:
                            st.warning(f"⚠️ {sentiment_errors} sentiment analysis errors for {ticker} (using neutral scores)")

                        average_sentiment = (
                            sum(stock_sentiment_scores) / len(stock_sentiment_scores)
                            if stock_sentiment_scores else 0
                        )

                        # Create breakdown for consistency
                        sentiment_breakdown = {
                            "traditional_sentiment": average_sentiment,
                            "social_sentiment": 0.0,
                            "weighted_sentiment": average_sentiment,
                            "traditional_count": len(news_headlines),
                            "social_count": 0,
                            "total_count": len(all_headlines),
                            "sentiment_breakdown": {
                                "traditional_scores": stock_sentiment_scores,
                                "social_scores": [],
                                "combined_scores": stock_sentiment_scores
                            }
                        }

                    sentiment_label = get_sentiment_label(average_sentiment)

                    # Calculate confidence metrics
                    confidence_metrics = get_sentiment_confidence(sentiment_breakdown["sentiment_breakdown"]["combined_scores"])

                    # Prepare result data
                    result_data = {
                        "ticker": ticker,
                        "average_sentiment": average_sentiment,
                        "sentiment_label": sentiment_label,
                        "headlines": all_headlines,
                        "headlines_count": len(all_headlines),
                        "traditional_news_count": len(news_headlines),
                        "social_media_count": len(all_headlines) - len(news_headlines),
                        "sentiment_breakdown": sentiment_breakdown,
                        "confidence_metrics": confidence_metrics,
                        "sentiment_errors": sentiment_breakdown.get("sentiment_errors", 0)
                    }

                    # Add stock data if available
                    if enable_stock_data and ticker in stock_data_results:
                        stock_info = stock_data_results[ticker]
                        if "error" not in stock_info:
                            result_data.update({
                                "stock_data": stock_info,
                                "current_price": stock_info.get("current_price", 0),
                                "price_change": stock_info.get("price_change", 0),
                                "price_change_percent": stock_info.get("price_change_percent", 0),
                                "market_cap": stock_info.get("market_cap", 0),
                                "pe_ratio": stock_info.get("pe_ratio", 0)
                            })

                    # Add social media data if available
                    if enable_social_media and ticker in social_media_data:
                        result_data["social_media_sources"] = social_media_data[ticker]

                    # Generate trading recommendation
                    if enable_stock_data and 'stock_data' in result_data:
                        recommendation = generate_trading_recommendation(
                            result_data.get('stock_data', {}),
                            sentiment_breakdown,
                            confidence_metrics
                        )
                        result_data['trading_recommendation'] = recommendation

                    stocks_sentiment_results.append(result_data)
                else:
                    failed_tickers.append({
                        "ticker": ticker,
                        "error": "No news headlines found"
                    })

            except Exception as e:
                st.error(f"❌ Critical error processing {ticker}: {e}")
                failed_tickers.append({"ticker": ticker, "error": f"Critical error: {e}"})

        # Clear progress indicators
        progress_bar.empty()
        status_text.empty()

        # Display results
        if stocks_sentiment_results or failed_tickers:
            # Summary statistics
            total_processed = len(stocks_sentiment_results)
            total_failed = len(failed_tickers)

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("✅ Successfully Processed", total_processed)
            with col2:
                st.metric("❌ Failed", total_failed)
            with col3:
                success_rate = (total_processed / (total_processed + total_failed)) * 100 if (total_processed + total_failed) > 0 else 0
                st.metric("📊 Success Rate", f"{success_rate:.1f}%")

            # Display successful results
            if stocks_sentiment_results:
                st.success(f"📈 Enhanced Stock Analysis Results ({total_processed} stocks):")

                # Create dashboard summary
                if enable_stock_data:
                    st.subheader("📊 Portfolio Dashboard")

                    # Calculate portfolio metrics
                    total_market_cap = 0
                    avg_sentiment = 0
                    buy_recommendations = 0
                    sell_recommendations = 0

                    for stock in stocks_sentiment_results:
                        avg_sentiment += stock['average_sentiment']
                        if 'market_cap' in stock:
                            total_market_cap += stock.get('market_cap', 0)
                        if 'trading_recommendation' in stock:
                            action = stock['trading_recommendation']['action']
                            if action in ['BUY', 'WEAK BUY']:
                                buy_recommendations += 1
                            elif action in ['SELL', 'WEAK SELL']:
                                sell_recommendations += 1

                    avg_sentiment = avg_sentiment / len(stocks_sentiment_results) if stocks_sentiment_results else 0

                    # Display portfolio metrics
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        sentiment_color = "🟢" if avg_sentiment > POSITIVE_SENTIMENT_THRESHOLD else "🔴" if avg_sentiment < -POSITIVE_SENTIMENT_THRESHOLD else "🟡"
                        st.metric("Portfolio Sentiment", f"{sentiment_color} {avg_sentiment:.3f}")

                    with col2:
                        if total_market_cap > 1e12:
                            st.metric("Total Market Cap", f"${total_market_cap/1e12:.1f}T")
                        elif total_market_cap > 1e9:
                            st.metric("Total Market Cap", f"${total_market_cap/1e9:.1f}B")
                        else:
                            st.metric("Total Market Cap", f"${total_market_cap/1e6:.1f}M")

                    with col3:
                        st.metric("Buy Signals", f"🟢 {buy_recommendations}")

                    with col4:
                        st.metric("Sell Signals", f"🔴 {sell_recommendations}")

                    # Top recommendations
                    if any('trading_recommendation' in stock for stock in stocks_sentiment_results):
                        st.subheader("🎯 Top Recommendations")

                        # Sort by recommendation score
                        sorted_by_score = sorted(
                            [s for s in stocks_sentiment_results if 'trading_recommendation' in s],
                            key=lambda x: x['trading_recommendation']['score'],
                            reverse=True
                        )

                        col1, col2 = st.columns(2)

                        with col1:
                            st.write("**🟢 Top Buy Candidates:**")
                            buy_candidates = [s for s in sorted_by_score if s['trading_recommendation']['action'] in ['BUY', 'WEAK BUY']][:3]
                            for stock in buy_candidates:
                                rec = stock['trading_recommendation']
                                st.write(f"• **{stock['ticker']}** - {rec['action']} (Score: {rec['score']:.3f})")

                        with col2:
                            st.write("**🔴 Top Sell Candidates:**")
                            sell_candidates = [s for s in sorted_by_score if s['trading_recommendation']['action'] in ['SELL', 'WEAK SELL']][:3]
                            for stock in sell_candidates:
                                rec = stock['trading_recommendation']
                                st.write(f"• **{stock['ticker']}** - {rec['action']} (Score: {rec['score']:.3f})")

                # Sort by sentiment score (most positive first)
                sorted_results = sorted(stocks_sentiment_results, key=lambda x: x['average_sentiment'], reverse=True)

                for stock_data in sorted_results:
                    ticker = stock_data['ticker']

                    # Create enhanced title with stock price if available
                    title_parts = [f"📊 {ticker}"]
                    if 'current_price' in stock_data and stock_data['current_price']:
                        price = stock_data['current_price']
                        change = stock_data.get('price_change', 0)
                        change_pct = stock_data.get('price_change_percent', 0)
                        price_color = "🟢" if change >= 0 else "🔴"
                        title_parts.append(f"${price:.2f} ({price_color}{change:+.2f}, {change_pct:+.1f}%)")
                    title_parts.append(f"- {stock_data['sentiment_label']} Sentiment")

                    with st.expander(" ".join(title_parts), expanded=True):
                        # Stock data metrics row
                        if 'stock_data' in stock_data:
                            st.subheader("📈 Stock Metrics")
                            col1, col2, col3, col4 = st.columns(4)

                            stock_info = stock_data['stock_data']
                            with col1:
                                st.metric("Current Price", f"${stock_data.get('current_price', 0):.2f}")
                            with col2:
                                change = stock_data.get('price_change', 0)
                                st.metric("Price Change", f"${change:.2f}", delta=f"{stock_data.get('price_change_percent', 0):.1f}%")
                            with col3:
                                market_cap = stock_data.get('market_cap', 0)
                                if market_cap > 1e9:
                                    st.metric("Market Cap", f"${market_cap/1e9:.1f}B")
                                elif market_cap > 1e6:
                                    st.metric("Market Cap", f"${market_cap/1e6:.1f}M")
                                else:
                                    st.metric("Market Cap", f"${market_cap:,.0f}")
                            with col4:
                                pe_ratio = stock_data.get('pe_ratio', 0)
                                st.metric("P/E Ratio", f"{pe_ratio:.2f}" if pe_ratio else "N/A")

                            # Additional stock info
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.write(f"**Company:** {stock_info.get('company_name', 'N/A')}")
                                st.write(f"**Sector:** {stock_info.get('sector', 'N/A')}")
                            with col2:
                                st.write(f"**Volume:** {stock_info.get('volume', 0):,}")
                                st.write(f"**Beta:** {stock_info.get('beta', 0):.2f}")
                            with col3:
                                st.write(f"**52W High:** ${stock_info.get('52_week_high', 0):.2f}")
                                st.write(f"**52W Low:** ${stock_info.get('52_week_low', 0):.2f}")

                        # Sentiment metrics row
                        st.subheader("📊 Sentiment Analysis")

                        # Main sentiment metrics
                        col1, col2, col3, col4 = st.columns(4)

                        with col1:
                            st.metric("Weighted Sentiment", f"{stock_data['average_sentiment']:.4f}")
                        with col2:
                            st.metric("Total Headlines", stock_data['headlines_count'])
                        with col3:
                            st.metric("Traditional News", stock_data.get('traditional_news_count', 0))
                        with col4:
                            st.metric("Social Media", stock_data.get('social_media_count', 0))

                        # Detailed sentiment breakdown if available
                        if 'sentiment_breakdown' in stock_data:
                            breakdown = stock_data['sentiment_breakdown']

                            st.write("**Sentiment Breakdown:**")
                            col1, col2, col3 = st.columns(3)

                            with col1:
                                trad_sentiment = breakdown.get('traditional_sentiment', 0)
                                trad_color = "🟢" if trad_sentiment > POSITIVE_SENTIMENT_THRESHOLD else "🔴" if trad_sentiment < -POSITIVE_SENTIMENT_THRESHOLD else "🟡"
                                st.metric("Traditional News", f"{trad_sentiment:.3f}", help="Sentiment from traditional news sources")
                                st.write(f"{trad_color} {get_sentiment_label(trad_sentiment)}")

                            with col2:
                                social_sentiment = breakdown.get('social_sentiment', 0)
                                social_color = "🟢" if social_sentiment > POSITIVE_SENTIMENT_THRESHOLD else "🔴" if social_sentiment < -POSITIVE_SENTIMENT_THRESHOLD else "🟡"
                                st.metric("Social Media", f"{social_sentiment:.3f}", help="Sentiment from social media sources")
                                st.write(f"{social_color} {get_sentiment_label(social_sentiment)}")

                            with col3:
                                if 'confidence_metrics' in stock_data:
                                    confidence = stock_data['confidence_metrics']
                                    st.metric("Confidence", confidence.get('confidence', 'Unknown'))
                                    st.write(f"Consistency: {confidence.get('consistency', 0):.2f}")

                        # Overall sentiment label with color
                        sentiment_color = "🟢" if stock_data['average_sentiment'] > POSITIVE_SENTIMENT_THRESHOLD else "🔴" if stock_data['average_sentiment'] < -POSITIVE_SENTIMENT_THRESHOLD else "🟡"
                        st.write(f"**Overall Sentiment:** {sentiment_color} {stock_data['sentiment_label']}")

                        # Show sentiment analysis errors if any
                        if stock_data.get('sentiment_errors', 0) > 0:
                            st.warning(f"⚠️ {stock_data['sentiment_errors']} sentiment analysis errors (neutral scores used)")

                        # Trading recommendation
                        if 'trading_recommendation' in stock_data:
                            st.subheader("💡 Trading Recommendation")
                            recommendation = stock_data['trading_recommendation']

                            col1, col2, col3 = st.columns(3)

                            with col1:
                                action = recommendation['action']
                                action_color = {
                                    'BUY': '🟢',
                                    'WEAK BUY': '🟡',
                                    'HOLD': '⚪',
                                    'WEAK SELL': '🟠',
                                    'SELL': '🔴'
                                }.get(action, '⚪')
                                st.metric("Recommendation", f"{action_color} {action}")

                            with col2:
                                confidence = recommendation['confidence']
                                st.metric("Confidence", confidence)

                            with col3:
                                risk_level = recommendation['risk_level']
                                st.metric("Risk Level", risk_level)

                            # Recommendation reasoning
                            st.write("**Analysis:**")
                            for reason in recommendation['reasoning']:
                                st.write(f"• {reason}")

                            # Recommendation score
                            score = recommendation['score']
                            st.write(f"**Overall Score:** {score:.3f} (Range: -1.0 to +1.0)")

                            # Add disclaimer
                            st.info("⚠️ **Disclaimer:** This recommendation is for educational purposes only and should not be considered as financial advice. Always do your own research and consult with a financial advisor before making investment decisions.")

                        # Interactive stock chart
                        if enable_charts and 'stock_data' in stock_data:
                            st.subheader("📈 Stock Chart")
                            chart = create_stock_chart(ticker, period=chart_period)
                            if chart:
                                st.plotly_chart(chart, use_container_width=True)
                            else:
                                st.warning(f"Unable to create chart for {ticker}")

                        # Headlines section
                        st.subheader("📰 Recent Headlines")

                        # Show headlines by source if social media is enabled
                        if enable_social_media and 'social_media_sources' in stock_data:
                            sources = stock_data['social_media_sources']

                            # Create tabs for different sources
                            tab_names = ["All Headlines"]
                            if 'reddit' in sources:
                                tab_names.append("Reddit")
                            if 'newsapi' in sources:
                                tab_names.append("NewsAPI")
                            if 'alpha_vantage' in sources:
                                tab_names.append("Alpha Vantage")

                            tabs = st.tabs(tab_names)

                            with tabs[0]:  # All headlines
                                for j, headline in enumerate(stock_data['headlines'][:10], 1):
                                    st.write(f"{j}. {headline}")

                            # Individual source tabs
                            tab_idx = 1
                            for source_name, articles in sources.items():
                                if source_name in ['reddit', 'newsapi', 'alpha_vantage'] and tab_idx < len(tabs):
                                    with tabs[tab_idx]:
                                        if source_name == 'reddit':
                                            for article in articles[:5]:
                                                st.write(f"**r/{article.get('subreddit', 'unknown')}:** {article.get('title', '')}")
                                                if article.get('score', 0) > 0:
                                                    st.write(f"   ↑ {article['score']} upvotes, {article.get('num_comments', 0)} comments")
                                        else:
                                            for article in articles[:5]:
                                                title = article.get('title', '')
                                                if title:
                                                    st.write(f"• {title}")
                                    tab_idx += 1
                        else:
                            # Simple headlines display
                            for j, headline in enumerate(stock_data['headlines'][:10], 1):
                                st.write(f"{j}. {headline}")

            # Display failed tickers
            if failed_tickers:
                st.error(f"❌ Failed to Process ({total_failed} stocks):")
                for failed in failed_tickers:
                    st.error(f"**{failed['ticker']}**: {failed['error']}")

                # Provide helpful suggestions
                st.info("💡 **Troubleshooting Tips:**")
                st.write("- Check if ticker symbols are correct (e.g., AAPL, TSLA, GOOGL)")
                st.write("- Verify your internet connection")
                st.write("- Check API key configuration in sidebar")
                st.write("- Some tickers may not have recent news coverage")
        else:
            st.info("No results to display. Please check your ticker symbols and try again.")

        # Add historical sentiment analysis
        if enable_historical and historical_data:
            st.header("📈 Historical Sentiment Analysis")
            
            for ticker in valid_tickers:
                if ticker in historical_data and historical_data[ticker]:
                    with st.expander(f"Historical Sentiment for {ticker}", expanded=True):
                        # Process historical data
                        dates = []
                        daily_sentiments = []
                        headline_counts = []
                        
                        # Sort dates chronologically
                        sorted_dates = sorted(historical_data[ticker].keys())
                        
                        for date in sorted_dates:
                            headlines = historical_data[ticker][date]
                            if headlines:
                                # Calculate sentiment for this date
                                daily_scores = []
                                for headline in headlines:
                                    try:
                                        score = analyze_sentiment_safe(headline, default_score=0.0)
                                        daily_scores.append(score)
                                    except Exception as e:
                                        logging.warning(f"Error analyzing sentiment: {e}")
                                
                                if daily_scores:
                                    dates.append(date)
                                    avg_daily_sentiment = sum(daily_scores) / len(daily_scores)
                                    daily_sentiments.append(avg_daily_sentiment)
                                    headline_counts.append(len(headlines))
                        
                        # Create DataFrame for visualization
                        if dates:
                            hist_data = pd.DataFrame({
                                'Date': dates,
                                'Sentiment': daily_sentiments,
                                'Headlines': headline_counts
                            })
                            
                            # Display sentiment trend chart
                            st.subheader(f"Sentiment Trend for {ticker}")
                            
                            # Create two columns for charts
                            col1, col2 = st.columns(2)
                            
                            with col1:
                                # Line chart for sentiment
                                st.line_chart(hist_data.set_index('Date')['Sentiment'])
                                
                            with col2:
                                # Bar chart for headline count
                                st.bar_chart(hist_data.set_index('Date')['Headlines'])
                            
                            # Show the data table
                            st.subheader("Daily Sentiment Data")
                            hist_data['Sentiment'] = hist_data['Sentiment'].round(4)
                            st.dataframe(hist_data)
                            
                            # Calculate trend indicators
                            if len(daily_sentiments) > 1:
                                sentiment_change = daily_sentiments[-1] - daily_sentiments[0]
                                trend_direction = "improving" if sentiment_change > 0 else "declining" if sentiment_change < 0 else "stable"
                                
                                st.info(f"📊 Sentiment trend is {trend_direction} over the past {len(dates)} days with data " +
                                       f"(change of {sentiment_change:.4f})")
                        else:
                            st.warning(f"No historical sentiment data available for {ticker}")
        else:
            st.info("No results to display. Please check your ticker symbols and try again.")

        # Add historical sentiment summary
        if enable_historical and historical_data:
            st.header("📊 Historical Sentiment Summary")
            
            # Prepare summary data
            summary_data = []
            for ticker in valid_tickers:
                if ticker in historical_data and historical_data[ticker]:
                    # Process historical data for summary
                    dates = []
                    daily_sentiments = []
                    
                    # Sort dates chronologically
                    sorted_dates = sorted(historical_data[ticker].keys())
                    
                    for date in sorted_dates:
                        headlines = historical_data[ticker][date]
                        if headlines:
                            daily_scores = []
                            for headline in headlines:
                                try:
                                    score = analyze_sentiment_safe(headline, default_score=0.0)
                                    daily_scores.append(score)
                                except Exception:
                                    pass
                            
                            if daily_scores:
                                dates.append(date)
                                daily_sentiments.append(sum(daily_scores) / len(daily_scores))
                    
                    if len(daily_sentiments) > 1:
                        # Calculate trend metrics
                        start_sentiment = daily_sentiments[0]
                        end_sentiment = daily_sentiments[-1]
                        min_sentiment = min(daily_sentiments)
                        max_sentiment = max(daily_sentiments)
                        avg_sentiment = sum(daily_sentiments) / len(daily_sentiments)
                        sentiment_change = end_sentiment - start_sentiment
                        volatility = pd.Series(daily_sentiments).std()
                        
                        summary_data.append({
                            "ticker": ticker,
                            "start_sentiment": start_sentiment,
                            "end_sentiment": end_sentiment,
                            "min_sentiment": min_sentiment,
                            "max_sentiment": max_sentiment,
                            "avg_sentiment": avg_sentiment,
                            "sentiment_change": sentiment_change,
                            "volatility": volatility,
                            "days_with_data": len(daily_sentiments)
                        })
            
            if summary_data:
                # Create summary DataFrame
                summary_df = pd.DataFrame(summary_data)
                
                # Sort by sentiment change (most improved first)
                summary_df = summary_df.sort_values("sentiment_change", ascending=False)
                
                # Round numeric columns
                numeric_cols = ["start_sentiment", "end_sentiment", "min_sentiment", 
                                "max_sentiment", "avg_sentiment", "sentiment_change", "volatility"]
                summary_df[numeric_cols] = summary_df[numeric_cols].round(4)
                
                # Display summary table
                st.dataframe(summary_df)
                
                # Highlight stocks with significant changes
                improving_stocks = summary_df[summary_df["sentiment_change"] > 0.1]["ticker"].tolist()
                declining_stocks = summary_df[summary_df["sentiment_change"] < -0.1]["ticker"].tolist()
                
                if improving_stocks:
                    st.success(f"📈 Stocks with improving sentiment: {', '.join(improving_stocks)}")
                
                if declining_stocks:
                    st.warning(f"📉 Stocks with declining sentiment: {', '.join(declining_stocks)}")
        else:
            st.info("No results to display. Please check your ticker symbols and try again.")
