"""
Consolidated data sources module for stock data, news, and social media.
Combines yfinance, news APIs, and social media into one streamlined module.
"""

import yfinance as yf
import requests
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv
import warnings

load_dotenv()
warnings.filterwarnings("ignore", category=FutureWarning, module="yfinance")

# API Configuration
MARKETAUX_API_KEY = os.getenv("MARKETAUX_API_KEY")
NEWSAPI_KEY = os.getenv("NEWSAPI_KEY")
MARKETAUX_URL = "https://api.marketaux.com/v1/news/all"

class DataSourceError(Exception):
    """Base exception for data source errors."""
    pass

def get_stock_data(ticker: str) -> Dict[str, Any]:
    """Get comprehensive stock data from yfinance."""
    try:
        stock = yf.Ticker(ticker.upper())
        info = stock.info
        
        if not info or 'symbol' not in info:
            return {"error": f"Ticker {ticker} not found"}
        
        # Get current price data
        current_price = info.get('currentPrice', info.get('regularMarketPrice', 0))
        previous_close = info.get('previousClose', 0)
        price_change = current_price - previous_close if current_price and previous_close else 0
        price_change_percent = (price_change / previous_close * 100) if previous_close else 0
        
        return {
            'symbol': info.get('symbol', ticker),
            'company_name': info.get('longName', info.get('shortName', 'N/A')),
            'current_price': current_price,
            'price_change': price_change,
            'price_change_percent': price_change_percent,
            'market_cap': info.get('marketCap', 0),
            'pe_ratio': info.get('trailingPE', 0),
            'volume': info.get('volume', 0),
            'sector': info.get('sector', 'N/A'),
            'beta': info.get('beta', 0),
            '52_week_high': info.get('fiftyTwoWeekHigh', 0),
            '52_week_low': info.get('fiftyTwoWeekLow', 0)
        }
    except Exception as e:
        return {"error": str(e)}

def get_stock_chart_data(ticker: str, period: str = "3mo") -> Optional[pd.DataFrame]:
    """Get historical stock data for charts."""
    try:
        stock = yf.Ticker(ticker.upper())
        hist = stock.history(period=period)
        
        if hist.empty:
            return None
            
        # Add simple technical indicators
        hist['sma_20'] = hist['Close'].rolling(window=20).mean()
        hist['sma_50'] = hist['Close'].rolling(window=50).mean()
        
        # RSI calculation (simplified)
        delta = hist['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        hist['rsi'] = 100 - (100 / (1 + rs))
        
        return hist
    except Exception:
        return None

def get_traditional_news(tickers: List[str], days_back: int = 7) -> List[str]:
    """Get traditional news headlines from Marketaux."""
    if not MARKETAUX_API_KEY:
        return []
    
    try:
        params = {
            "api_token": MARKETAUX_API_KEY,
            "symbols": ",".join(tickers),
            "limit": 20
        }
        
        response = requests.get(MARKETAUX_URL, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        headlines = []
        if data and 'data' in data:
            for article in data['data']:
                title = article.get('title')
                if title:
                    headlines.append(title)
        
        return headlines
    except Exception:
        return []

def get_reddit_posts(ticker: str, limit: int = 5) -> List[str]:
    """Get Reddit posts about a stock ticker."""
    headlines = []
    subreddits = ['stocks', 'investing']
    
    for subreddit in subreddits:
        try:
            url = f"https://www.reddit.com/r/{subreddit}/search.json"
            params = {
                'q': ticker,
                'restrict_sr': 'true',
                'sort': 'new',
                'limit': limit,
                't': 'week'
            }
            headers = {'User-Agent': 'StockScanner/1.0'}
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if 'data' in data and 'children' in data['data']:
                for post in data['data']['children']:
                    post_data = post.get('data', {})
                    title = post_data.get('title', '')
                    if ticker.upper() in title.upper():
                        headlines.append(title)
        except Exception:
            continue
    
    return headlines[:limit]

def get_newsapi_articles(ticker: str, limit: int = 5) -> List[str]:
    """Get additional news from NewsAPI if configured."""
    if not NEWSAPI_KEY:
        return []
    
    try:
        url = "https://newsapi.org/v2/everything"
        params = {
            'q': f'"{ticker}" stock',
            'language': 'en',
            'sortBy': 'publishedAt',
            'pageSize': limit,
            'apiKey': NEWSAPI_KEY
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        headlines = []
        if data.get('status') == 'ok' and 'articles' in data:
            for article in data['articles']:
                title = article.get('title')
                if title:
                    headlines.append(title)
        
        return headlines
    except Exception:
        return []

def get_all_news(ticker: str) -> Dict[str, List[str]]:
    """Get news from all available sources."""
    return {
        'traditional': get_traditional_news([ticker]),
        'reddit': get_reddit_posts(ticker),
        'newsapi': get_newsapi_articles(ticker)
    }

def analyze_sentiment_simple(text: str) -> float:
    """Simple sentiment analysis using basic word matching."""
    if not text:
        return 0.0
    
    text = text.lower()
    
    # Simple positive/negative word lists
    positive_words = [
        'good', 'great', 'excellent', 'positive', 'up', 'rise', 'gain', 'profit',
        'bull', 'bullish', 'strong', 'growth', 'increase', 'buy', 'moon', 'rocket'
    ]
    
    negative_words = [
        'bad', 'terrible', 'negative', 'down', 'fall', 'loss', 'bear', 'bearish',
        'weak', 'decline', 'decrease', 'sell', 'crash', 'drop', 'plunge'
    ]
    
    positive_count = sum(1 for word in positive_words if word in text)
    negative_count = sum(1 for word in negative_words if word in text)
    
    # Simple scoring
    if positive_count + negative_count == 0:
        return 0.0
    
    score = (positive_count - negative_count) / (positive_count + negative_count)
    return max(-1.0, min(1.0, score))

def calculate_sentiment_score(headlines: List[str]) -> Dict[str, Any]:
    """Calculate sentiment score from headlines."""
    if not headlines:
        return {'score': 0.0, 'count': 0, 'confidence': 'No Data'}
    
    scores = [analyze_sentiment_simple(headline) for headline in headlines]
    avg_score = sum(scores) / len(scores)
    
    # Simple confidence based on count and consistency
    std_dev = np.std(scores) if len(scores) > 1 else 0
    confidence = 'High' if len(scores) >= 5 and std_dev < 0.5 else 'Medium' if len(scores) >= 3 else 'Low'
    
    return {
        'score': avg_score,
        'count': len(headlines),
        'confidence': confidence,
        'individual_scores': scores
    }

def generate_recommendation(stock_data: Dict, sentiment_data: Dict) -> Dict[str, Any]:
    """Generate simple trading recommendation."""
    score = 0.0
    reasons = []
    
    # Sentiment factor (50%)
    sentiment_score = sentiment_data.get('score', 0)
    if sentiment_score > 0.2:
        score += 0.5
        reasons.append(f"Positive sentiment ({sentiment_score:.2f})")
    elif sentiment_score < -0.2:
        score -= 0.5
        reasons.append(f"Negative sentiment ({sentiment_score:.2f})")
    
    # Price momentum factor (30%)
    price_change = stock_data.get('price_change_percent', 0)
    if price_change > 2:
        score += 0.3
        reasons.append(f"Positive momentum (+{price_change:.1f}%)")
    elif price_change < -2:
        score -= 0.3
        reasons.append(f"Negative momentum ({price_change:.1f}%)")
    
    # Valuation factor (20%)
    pe_ratio = stock_data.get('pe_ratio', 0)
    if pe_ratio > 0:
        if pe_ratio < 15:
            score += 0.2
            reasons.append(f"Attractive valuation (P/E: {pe_ratio:.1f})")
        elif pe_ratio > 30:
            score -= 0.2
            reasons.append(f"High valuation (P/E: {pe_ratio:.1f})")
    
    # Determine action
    if score > 0.4:
        action = "BUY"
        color = "🟢"
    elif score > 0.1:
        action = "WEAK BUY"
        color = "🟡"
    elif score < -0.4:
        action = "SELL"
        color = "🔴"
    elif score < -0.1:
        action = "WEAK SELL"
        color = "🟠"
    else:
        action = "HOLD"
        color = "⚪"
    
    return {
        'action': action,
        'color': color,
        'score': score,
        'reasons': reasons,
        'confidence': sentiment_data.get('confidence', 'Medium')
    }

def check_data_sources() -> Dict[str, bool]:
    """Check which data sources are available."""
    return {
        'yfinance': True,  # Always available
        'marketaux': bool(MARKETAUX_API_KEY),
        'newsapi': bool(NEWSAPI_KEY),
        'reddit': True  # No API key needed
    }
