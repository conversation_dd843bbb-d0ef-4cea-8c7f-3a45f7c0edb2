#!/usr/bin/env python3
"""
Setup validation script for Enhanced Stock Scanner.

This script validates that all components are properly installed and configured.
"""

import sys
import os
from datetime import datetime

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_status(component, status, message=""):
    """Print component status."""
    status_symbol = "✅" if status else "❌"
    print(f"{status_symbol} {component:<30} {message}")

def test_imports():
    """Test all required imports."""
    print_header("Testing Imports")
    
    imports_to_test = [
        ("streamlit", "streamlit"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("requests", "requests"),
        ("yfinance", "yfinance"),
        ("plotly", "plotly.graph_objects"),
        ("nltk", "nltk"),
        ("python-dotenv", "dotenv"),
    ]
    
    all_imports_ok = True
    
    for package_name, import_name in imports_to_test:
        try:
            __import__(import_name)
            print_status(package_name, True, "Imported successfully")
        except ImportError as e:
            print_status(package_name, False, f"Import failed: {e}")
            all_imports_ok = False
    
    return all_imports_ok

def test_custom_modules():
    """Test custom module imports."""
    print_header("Testing Custom Modules")
    
    modules_to_test = [
        "stock_data",
        "social_media_news", 
        "news_fetch",
        "sentiment_analyzer",
        "config_validator"
    ]
    
    all_modules_ok = True
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print_status(module_name, True, "Module loaded successfully")
        except ImportError as e:
            print_status(module_name, False, f"Module failed: {e}")
            all_modules_ok = False
    
    return all_modules_ok

def test_configuration():
    """Test configuration validation."""
    print_header("Testing Configuration")
    
    try:
        from config_validator import run_full_configuration_check
        
        config_result = run_full_configuration_check()
        
        print_status("Configuration Check", True, "Validation completed")
        print(f"   Overall Valid: {config_result['overall_valid']}")
        print(f"   Total Errors: {config_result['summary']['total_errors']}")
        print(f"   Total Warnings: {config_result['summary']['total_warnings']}")
        
        # Print component status
        components = ['environment', 'api', 'sentiment_analyzer', 'stock_data', 'social_media_news']
        for component in components:
            if component in config_result:
                status = config_result[component].get('valid', False)
                print_status(f"  {component}", status)
        
        return config_result['overall_valid']
        
    except Exception as e:
        print_status("Configuration Check", False, f"Error: {e}")
        return False

def test_stock_data():
    """Test stock data functionality."""
    print_header("Testing Stock Data")
    
    try:
        from stock_data import get_stock_info, get_stock_status
        
        # Test stock status
        status = get_stock_status()
        print_status("yfinance Available", status['yfinance_available'])
        print_status("Test Ticker Access", status['test_ticker_accessible'])
        
        if status['error_message']:
            print(f"   Error: {status['error_message']}")
        
        # Test stock info retrieval (if available)
        if status['test_ticker_accessible']:
            try:
                stock_info = get_stock_info("AAPL")
                print_status("Stock Info Retrieval", True, f"AAPL: ${stock_info.get('current_price', 'N/A')}")
            except Exception as e:
                print_status("Stock Info Retrieval", False, f"Error: {e}")
        
        return status['test_ticker_accessible']
        
    except Exception as e:
        print_status("Stock Data Test", False, f"Error: {e}")
        return False

def test_social_media():
    """Test social media news functionality."""
    print_header("Testing Social Media News")
    
    try:
        from social_media_news import get_social_media_status
        
        status = get_social_media_status()
        
        print_status("Reddit Access", status['reddit_accessible'])
        print_status("NewsAPI Configured", status['newsapi_configured'])
        print_status("Alpha Vantage Configured", status['alpha_vantage_configured'])
        
        print(f"   Available Sources: {', '.join(status['available_sources'])}")
        
        if status['error_messages']:
            print("   Errors:")
            for error in status['error_messages']:
                print(f"     - {error}")
        
        return len(status['available_sources']) > 0
        
    except Exception as e:
        print_status("Social Media Test", False, f"Error: {e}")
        return False

def test_sentiment_analysis():
    """Test sentiment analysis functionality."""
    print_header("Testing Sentiment Analysis")
    
    try:
        from sentiment_analyzer import analyze_sentiment_safe, get_sentiment_analyzer_status
        
        status = get_sentiment_analyzer_status()
        
        print_status("NLTK Available", status['nltk_available'])
        print_status("VADER Lexicon", status['vader_lexicon_available'])
        print_status("Analyzer Functional", status['analyzer_functional'])
        
        if status['error_message']:
            print(f"   Error: {status['error_message']}")
        
        # Test sentiment analysis
        if status['analyzer_functional']:
            test_text = "This is a positive test message about stocks"
            sentiment = analyze_sentiment_safe(test_text)
            print_status("Sentiment Test", True, f"Score: {sentiment:.3f}")
        
        return status['analyzer_functional']
        
    except Exception as e:
        print_status("Sentiment Analysis Test", False, f"Error: {e}")
        return False

def main():
    """Run all validation tests."""
    print_header("Enhanced Stock Scanner - Setup Validation")
    print(f"Validation Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    results = {
        "imports": test_imports(),
        "modules": test_custom_modules(),
        "configuration": test_configuration(),
        "stock_data": test_stock_data(),
        "social_media": test_social_media(),
        "sentiment": test_sentiment_analysis()
    }
    
    # Summary
    print_header("Validation Summary")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, result in results.items():
        print_status(test_name.replace("_", " ").title(), result)
    
    print(f"\nOverall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 All tests passed! Your Enhanced Stock Scanner is ready to use.")
        print("\nTo start the application, run:")
        print("   streamlit run stock_scanner_app.py")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} test(s) failed. Please check the errors above.")
        print("\nRecommendations:")
        
        if not results["imports"]:
            print("   - Install missing packages: pip install -r requirements.txt")
        
        if not results["configuration"]:
            print("   - Check your .env file and API keys")
        
        if not results["sentiment"]:
            print("   - NLTK VADER lexicon will download automatically on first use")
        
        print("\nFor detailed setup instructions, see README.md")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
