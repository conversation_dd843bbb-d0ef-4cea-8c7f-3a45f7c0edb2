"""
Stock data module for fetching comprehensive stock information using yfinance.

This module provides functions to fetch real-time stock prices, historical data,
company information, and financial metrics using the yfinance library.
"""

import yfinance as yf
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import warnings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Suppress yfinance warnings
warnings.filterwarnings("ignore", category=FutureWarning, module="yfinance")

class StockDataError(Exception):
    """Custom exception for stock data related errors."""
    pass

class TickerNotFoundError(StockDataError):
    """Exception raised when a ticker symbol is not found."""
    pass

class DataFetchError(StockDataError):
    """Exception raised when data fetching fails."""
    pass

def validate_ticker_symbol(ticker: str) -> str:
    """
    Validate and clean ticker symbol.
    
    Args:
        ticker: Stock ticker symbol
        
    Returns:
        str: Cleaned ticker symbol
        
    Raises:
        StockDataError: If ticker is invalid
    """
    if not ticker or not isinstance(ticker, str):
        raise StockDataError("Ticker symbol must be a non-empty string")
    
    # Clean and validate ticker
    ticker = ticker.strip().upper()
    
    if not ticker:
        raise StockDataError("Ticker symbol cannot be empty")
    
    # Basic validation - alphanumeric with dots and hyphens
    if not ticker.replace('.', '').replace('-', '').isalnum():
        raise StockDataError(f"Invalid ticker format: {ticker}")
    
    return ticker

def get_stock_info(ticker: str) -> Dict[str, Any]:
    """
    Get comprehensive stock information including company details and key metrics.
    
    Args:
        ticker: Stock ticker symbol
        
    Returns:
        Dict[str, Any]: Stock information dictionary
        
    Raises:
        StockDataError: If data fetching fails
    """
    try:
        ticker = validate_ticker_symbol(ticker)
        stock = yf.Ticker(ticker)
        
        # Get stock info
        info = stock.info
        
        if not info or 'symbol' not in info:
            raise TickerNotFoundError(f"Ticker {ticker} not found or has no data")
        
        # Extract key information with safe defaults
        stock_data = {
            'symbol': info.get('symbol', ticker),
            'company_name': info.get('longName', info.get('shortName', 'N/A')),
            'sector': info.get('sector', 'N/A'),
            'industry': info.get('industry', 'N/A'),
            'market_cap': info.get('marketCap', 0),
            'current_price': info.get('currentPrice', info.get('regularMarketPrice', 0)),
            'previous_close': info.get('previousClose', 0),
            'open_price': info.get('open', info.get('regularMarketOpen', 0)),
            'day_high': info.get('dayHigh', info.get('regularMarketDayHigh', 0)),
            'day_low': info.get('dayLow', info.get('regularMarketDayLow', 0)),
            'volume': info.get('volume', info.get('regularMarketVolume', 0)),
            'avg_volume': info.get('averageVolume', 0),
            'pe_ratio': info.get('trailingPE', 0),
            'forward_pe': info.get('forwardPE', 0),
            'price_to_book': info.get('priceToBook', 0),
            'dividend_yield': info.get('dividendYield', 0),
            'beta': info.get('beta', 0),
            '52_week_high': info.get('fiftyTwoWeekHigh', 0),
            '52_week_low': info.get('fiftyTwoWeekLow', 0),
            'currency': info.get('currency', 'USD'),
            'exchange': info.get('exchange', 'N/A'),
            'website': info.get('website', ''),
            'business_summary': info.get('longBusinessSummary', '')[:500] if info.get('longBusinessSummary') else ''
        }
        
        # Calculate additional metrics
        if stock_data['current_price'] and stock_data['previous_close']:
            price_change = stock_data['current_price'] - stock_data['previous_close']
            price_change_percent = (price_change / stock_data['previous_close']) * 100
            stock_data['price_change'] = price_change
            stock_data['price_change_percent'] = price_change_percent
        else:
            stock_data['price_change'] = 0
            stock_data['price_change_percent'] = 0
        
        logger.info(f"Successfully fetched stock info for {ticker}")
        return stock_data
        
    except TickerNotFoundError:
        raise
    except Exception as e:
        logger.error(f"Error fetching stock info for {ticker}: {e}")
        raise DataFetchError(f"Failed to fetch stock info for {ticker}: {e}")

def get_historical_data(ticker: str, period: str = "1mo", interval: str = "1d") -> pd.DataFrame:
    """
    Get historical stock price data.
    
    Args:
        ticker: Stock ticker symbol
        period: Data period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
        interval: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
        
    Returns:
        pd.DataFrame: Historical price data
        
    Raises:
        StockDataError: If data fetching fails
    """
    try:
        ticker = validate_ticker_symbol(ticker)
        stock = yf.Ticker(ticker)
        
        # Fetch historical data
        hist_data = stock.history(period=period, interval=interval)
        
        if hist_data.empty:
            raise DataFetchError(f"No historical data found for {ticker}")
        
        # Clean column names
        hist_data.columns = [col.lower().replace(' ', '_') for col in hist_data.columns]
        
        # Add calculated fields
        if 'close' in hist_data.columns:
            hist_data['daily_return'] = hist_data['close'].pct_change()
            hist_data['cumulative_return'] = (1 + hist_data['daily_return']).cumprod() - 1
        
        logger.info(f"Successfully fetched {len(hist_data)} historical data points for {ticker}")
        return hist_data
        
    except Exception as e:
        logger.error(f"Error fetching historical data for {ticker}: {e}")
        raise DataFetchError(f"Failed to fetch historical data for {ticker}: {e}")

def get_multiple_stock_data(tickers: List[str]) -> Dict[str, Dict[str, Any]]:
    """
    Get stock information for multiple tickers.
    
    Args:
        tickers: List of stock ticker symbols
        
    Returns:
        Dict[str, Dict[str, Any]]: Dictionary mapping tickers to their stock data
    """
    results = {}
    failed_tickers = []
    
    for ticker in tickers:
        try:
            stock_data = get_stock_info(ticker)
            results[ticker] = stock_data
        except Exception as e:
            logger.warning(f"Failed to fetch data for {ticker}: {e}")
            failed_tickers.append(ticker)
            results[ticker] = {
                'symbol': ticker,
                'error': str(e),
                'company_name': 'N/A',
                'current_price': 0,
                'price_change': 0,
                'price_change_percent': 0
            }
    
    if failed_tickers:
        logger.warning(f"Failed to fetch data for {len(failed_tickers)} tickers: {failed_tickers}")
    
    return results

def calculate_technical_indicators(hist_data: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate basic technical indicators for historical data.
    
    Args:
        hist_data: Historical price data DataFrame
        
    Returns:
        pd.DataFrame: Data with technical indicators added
    """
    try:
        data = hist_data.copy()
        
        if 'close' not in data.columns:
            logger.warning("No 'close' column found in historical data")
            return data
        
        # Simple Moving Averages
        data['sma_20'] = data['close'].rolling(window=20).mean()
        data['sma_50'] = data['close'].rolling(window=50).mean()
        
        # Exponential Moving Averages
        data['ema_12'] = data['close'].ewm(span=12).mean()
        data['ema_26'] = data['close'].ewm(span=26).mean()
        
        # MACD
        data['macd'] = data['ema_12'] - data['ema_26']
        data['macd_signal'] = data['macd'].ewm(span=9).mean()
        data['macd_histogram'] = data['macd'] - data['macd_signal']
        
        # RSI (Relative Strength Index)
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        data['bb_middle'] = data['close'].rolling(window=20).mean()
        bb_std = data['close'].rolling(window=20).std()
        data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
        data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
        
        logger.info("Successfully calculated technical indicators")
        return data
        
    except Exception as e:
        logger.error(f"Error calculating technical indicators: {e}")
        return hist_data

def get_stock_status() -> Dict[str, Any]:
    """
    Check the status of the yfinance stock data service.
    
    Returns:
        Dict[str, Any]: Status information
    """
    status = {
        "yfinance_available": False,
        "test_ticker_accessible": False,
        "error_message": None
    }
    
    try:
        # Test yfinance import
        import yfinance as yf
        status["yfinance_available"] = True
        
        # Test with a known ticker
        test_ticker = yf.Ticker("AAPL")
        test_info = test_ticker.info
        
        if test_info and 'symbol' in test_info:
            status["test_ticker_accessible"] = True
        else:
            status["error_message"] = "Unable to fetch test ticker data"
            
    except ImportError as e:
        status["error_message"] = f"yfinance not available: {e}"
    except Exception as e:
        status["error_message"] = f"Error testing stock data service: {e}"
    
    return status
